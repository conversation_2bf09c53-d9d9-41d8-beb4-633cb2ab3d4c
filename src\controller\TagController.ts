/**
 * TagController - <PERSON>les tag operations
 *
 * This class provides methods for creating, updating, and deleting tags.
 * It uses the CommandManager to provide undo/redo functionality.
 */

import { commandManager } from './CommandManager';
import { EventEmitter } from '../model/EventEmitter';
import { v4 as uuidv4 } from 'uuid';

export interface Tag {
  id: string;
  name: string;
  color: string;
  description?: string;
  favorite?: boolean;
  createdAt: number;
  updatedAt: number;
}

export enum TagEvents {
  TAG_CREATED = 'tag:created',
  TAG_UPDATED = 'tag:updated',
  TAG_DELETED = 'tag:deleted',
  TAGS_LOADED = 'tags:loaded'
}

export class TagController extends EventEmitter {
  private tags: Map<string, Tag> = new Map();
  private storageKey = 'mvc-tags';

  constructor() {
    super();

    // Load tags from localStorage
    this.loadFromStorage();
  }

  /**
   * Create a new tag
   * @param name The tag name
   * @param color The tag color
   * @param description The tag description
   * @returns The created tag
   */
  createTag(name: string, color: string = '#4299e1', description?: string): Tag {
    // Generate a unique ID - using just the UUID without the prefix
    const id = uuidv4();

    // Create the tag
    const tag: Tag = {
      id,
      name,
      color,
      description,
      createdAt: Date.now(),
      updatedAt: Date.now()
    };

    // Create command
    const command = {
      execute: () => {
        this.tags.set(id, tag);
        this.emit(TagEvents.TAG_CREATED, tag);
        this.saveToStorage();
        return tag;
      },
      undo: () => {
        this.tags.delete(id);
        this.emit(TagEvents.TAG_DELETED, id);
        this.saveToStorage();
      },
      redo: () => {
        this.tags.set(id, tag);
        this.emit(TagEvents.TAG_CREATED, tag);
        this.saveToStorage();
      },
      description: `Create tag "${name}"`
    };

    // Execute command
    return commandManager.execute(command);
  }

  /**
   * Update a tag
   * @param id The tag ID
   * @param updates The updates to apply
   * @returns The updated tag or null if not found
   */
  updateTag(id: string, updates: Partial<Omit<Tag, 'id' | 'createdAt' | 'updatedAt'>>): Tag | null {
    const tag = this.tags.get(id);
    if (!tag) return null;

    // Store original values for undo
    const originalValues: Partial<Tag> = {};

    // For each property in updates, store the original value
    Object.keys(updates).forEach(key => {
      (originalValues as any)[key] = (tag as any)[key];
    });

    // Create updated tag
    const updatedTag: Tag = {
      ...tag,
      ...updates,
      updatedAt: Date.now()
    };

    // Create command
    const command = {
      execute: () => {
        this.tags.set(id, updatedTag);
        this.emit(TagEvents.TAG_UPDATED, updatedTag);
        this.saveToStorage();
        return updatedTag;
      },
      undo: () => {
        const restoredTag: Tag = {
          ...tag,
          ...originalValues
        };
        this.tags.set(id, restoredTag);
        this.emit(TagEvents.TAG_UPDATED, restoredTag);
        this.saveToStorage();
      },
      redo: () => {
        this.tags.set(id, updatedTag);
        this.emit(TagEvents.TAG_UPDATED, updatedTag);
        this.saveToStorage();
      },
      description: `Update tag "${tag.name}"`
    };

    // Execute command
    return commandManager.execute(command);
  }

  /**
   * Delete a tag
   * @param id The tag ID
   * @returns True if the tag was deleted, false if not found
   */
  deleteTag(id: string): boolean {
    const tag = this.tags.get(id);
    if (!tag) return false;

    // Create command
    const command = {
      execute: () => {
        this.tags.delete(id);
        this.emit(TagEvents.TAG_DELETED, id);
        this.saveToStorage();
        return true;
      },
      undo: () => {
        this.tags.set(id, tag);
        this.emit(TagEvents.TAG_CREATED, tag);
        this.saveToStorage();
      },
      redo: () => {
        this.tags.delete(id);
        this.emit(TagEvents.TAG_DELETED, id);
        this.saveToStorage();
      },
      description: `Delete tag "${tag.name}"`
    };

    // Execute command
    return commandManager.execute(command);
  }

  /**
   * Get a tag by ID
   * @param id The tag ID
   * @returns The tag or null if not found
   */
  getTag(id: string): Tag | null {
    return this.tags.get(id) || null;
  }

  /**
   * Get all tags
   * @returns Array of all tags
   */
  getAllTags(): Tag[] {
    return Array.from(this.tags.values());
  }

  /**
   * Toggle a tag's favorite status
   * @param id The tag ID
   * @param favorite Whether the tag should be marked as favorite
   * @returns The updated tag or null if not found
   */
  toggleTagFavorite(id: string, favorite: boolean): Tag | null {
    const tag = this.tags.get(id);
    if (!tag) return null;

    // Create updated tag
    const updatedTag: Tag = {
      ...tag,
      favorite,
      updatedAt: Date.now()
    };

    // Create command
    const command = {
      execute: () => {
        this.tags.set(id, updatedTag);
        this.emit(TagEvents.TAG_UPDATED, updatedTag);
        this.saveToStorage();
        return updatedTag;
      },
      undo: () => {
        this.tags.set(id, tag);
        this.emit(TagEvents.TAG_UPDATED, tag);
        this.saveToStorage();
      },
      redo: () => {
        this.tags.set(id, updatedTag);
        this.emit(TagEvents.TAG_UPDATED, updatedTag);
        this.saveToStorage();
      },
      description: `${favorite ? 'Add' : 'Remove'} tag "${tag.name}" ${favorite ? 'to' : 'from'} favorites`
    };

    // Execute command
    return commandManager.execute(command);
  }

  /**
   * Get all favorite tags
   * @returns Array of favorite tags
   */
  getFavoriteTags(): Tag[] {
    return Array.from(this.tags.values()).filter(tag => tag.favorite === true);
  }

  /**
   * Get all non-favorite tags
   * @returns Array of non-favorite tags
   */
  getNonFavoriteTags(): Tag[] {
    return Array.from(this.tags.values()).filter(tag => tag.favorite !== true);
  }

  /**
   * Load tags from storage
   * @param tags Array of tags to load
   */
  loadTags(tags: Tag[]): void {
    // Clear existing tags
    this.tags.clear();

    // Add new tags
    tags.forEach(tag => {
      this.tags.set(tag.id, tag);
    });

    // Emit event
    this.emit(TagEvents.TAGS_LOADED, tags);
  }

  /**
   * Get tags as an array for storage
   * @returns Array of tags
   */
  getTagsForStorage(): Tag[] {
    return this.getAllTags();
  }

  /**
   * Get predefined tag colors
   * @returns Array of predefined colors
   */
  getPredefinedColors(): string[] {
    return [
      '#4299e1', // Blue
      '#48bb78', // Green
      '#ed8936', // Orange
      '#9f7aea', // Purple
      '#f56565', // Red
      '#ecc94b', // Yellow
      '#38b2ac', // Teal
      '#f687b3', // Pink
      '#a0aec0'  // Gray
    ];
  }

  /**
   * Save tags to localStorage
   */
  private saveToStorage(): void {
    try {
      const tagsArray = this.getAllTags();
      localStorage.setItem(this.storageKey, JSON.stringify(tagsArray));
      console.log('Tags saved to localStorage:', tagsArray);
    } catch (error) {
      console.error('Error saving tags to localStorage:', error);
    }
  }

  /**
   * Load tags from localStorage
   */
  private loadFromStorage(): void {
    try {
      const tagsJson = localStorage.getItem(this.storageKey);

      if (tagsJson) {
        const tagsArray = JSON.parse(tagsJson) as Tag[];

        // Clear existing tags
        this.tags.clear();

        // Add loaded tags
        tagsArray.forEach(tag => {
          this.tags.set(tag.id, tag);
        });

        console.log('Tags loaded from localStorage:', tagsArray);

        // Emit event
        this.emit(TagEvents.TAGS_LOADED, tagsArray);
      }
    } catch (error) {
      console.error('Error loading tags from localStorage:', error);
    }
  }
}

// Create a singleton instance
export const tagController = new TagController();
