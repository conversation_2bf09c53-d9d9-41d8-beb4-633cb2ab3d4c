import React from 'react';
import { Dialog } from 'primereact/dialog';
import { Button } from 'primereact/button';
import './DryRunWarningModal.css';

interface DryRunWarningModalProps {
  visible: boolean;
  onHide: () => void;
  onProceed: () => void;
  onExport: () => void;
  onUpgrade: () => void;
  isExitingDryRun?: boolean;
}

const DryRunWarningModal: React.FC<DryRunWarningModalProps> = ({
  visible,
  onHide,
  onProceed,
  onExport,
  onUpgrade,
  isExitingDryRun = false
}) => {
  return (
    <Dialog
      visible={visible}
      onHide={onHide}
      header={isExitingDryRun ? "Exit Dry Run Mode" : "Create New Project"}
      className="dry-run-warning-modal"
      style={{ width: '500px' }}
      modal
      blockScroll={true}
      dismissableMask={false}
      closeOnEscape={false}
      closable={false}
      footer={(
        <div className="dry-run-warning-footer">
          <Button
            label="Cancel"
            icon="pi pi-times"
            className="p-button-text"
            onClick={onHide}
          />
          <Button
            label={isExitingDryRun ? "Proceed Without Exporting" : "Create New Project"}
            icon={isExitingDryRun ? "pi pi-arrow-right" : "pi pi-plus"}
            className="p-button-danger"
            onClick={onProceed}
          />
        </div>
      )}
    >
      <div className="dry-run-warning-content">
        <div className="warning-icon">
          <i className="pi pi-exclamation-triangle"></i>
        </div>
        <div className="warning-message">
          <p>
            {isExitingDryRun
              ? 'You have exited Dry Run mode. Your project is not saved to your account.'
              : 'You are about to create a new project in Dry Run mode. Your current work will be lost.'}
          </p>
          <p>
            {isExitingDryRun
              ? 'Before proceeding, you have these options:'
              : 'Free accounts are limited to one saved project. You have these options:'}
          </p>

          <div className="warning-actions">
            <Button
              label="Export Current Project"
              icon="pi pi-download"
              className="p-button-outlined p-button-info"
              onClick={onExport}
            />
            <Button
              label="Upgrade Account"
              icon="pi pi-arrow-up"
              className="p-button-outlined p-button-success"
              onClick={onUpgrade}
            />
          </div>
        </div>
      </div>
    </Dialog>
  );
};

export default DryRunWarningModal;
