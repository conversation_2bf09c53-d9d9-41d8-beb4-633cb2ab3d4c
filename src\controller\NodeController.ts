/**
 * NodeController - Handles node operations
 *
 * This class provides methods for creating, updating, and deleting nodes.
 * It uses the CommandManager to provide undo/redo functionality.
 */

import { nodeStore } from '../model/NodeStore';
import { NodeData, NodeModel, NodeDimensions } from '../model/NodeModel';
import { EdgeData } from '../model/EdgeModel';
import { commandManager, BatchCommand } from './CommandManager';
import { layoutController } from './LayoutController';
import { mvcGraphLayoutService } from '../services/MVCGraphLayoutService';
import {
  generateHierarchicalId,
  hierarchicalIdToLabel
} from '../utils/hierarchicalIdUtils';
import {
  generateNodeId,
  generateTemporaryNodeId
} from '../utils/nodeIdUtils';
import { projectController } from './ProjectController';
import { deleteModalService } from '../services/DeleteModalService';
import { colorPickerService } from '../services/ColorPickerService';
// commandManager is already imported above

export class NodeController {
  private activeNodeEditorId: string | null = null;
  /**
   * Create a new node
   * @param nodeData The node data (partial)
   * @returns The ID of the created node
   */
  createNode(nodeData: Partial<NodeData> = {}): string {
    // Generate a hierarchical ID if not provided
    if (!nodeData.hierarchicalId) {
      nodeData.hierarchicalId = nodeData.hierarchicalId || '0-temp';
    }

    // Generate an immutable node ID if not provided
    if (!nodeData.id) {
      // Get the current project ID
      const projectId = projectController.getCurrentProject()?.id;

      // If we have a project ID, use it to generate a node ID
      if (projectId) {
        nodeData.id = generateNodeId(projectId, nodeData.hierarchicalId);
      } else {
        // If no project ID is available (e.g., in demo mode), generate a temporary ID
        nodeData.id = generateTemporaryNodeId(nodeData.hierarchicalId);
      }

      console.log(`Generated immutable node ID: ${nodeData.id} for hierarchical ID: ${nodeData.hierarchicalId}`);
    }

    // Apply default data
    const defaultNodeData: Partial<NodeData> = {
      title: 'New Node',
      content: '',
      relationships: {
        childIds: []
      },
      tags: [],
      completed: false,
      collapsed: false,
      manuallyPositioned: false
    };

    // Ensure position is valid and not NaN/undefined
    if (!nodeData.position ||
        nodeData.position.x === undefined || isNaN(nodeData.position.x) || !isFinite(nodeData.position.x) ||
        nodeData.position.y === undefined || isNaN(nodeData.position.y) || !isFinite(nodeData.position.y)) {
      nodeData.position = { x: 0, y: 0 };
      console.log(`Initializing node with default position (0,0) because position was invalid: ${JSON.stringify(nodeData.position)}`);
    }

    // CRITICAL: Always set dimensions based on type, even if dimensions are already provided
    // This guarantees that dimensions will always be valid and appropriate for the node type
    // Get type from node data or use default
    const nodeType = nodeData.type || 'richtext';

    // Always get fresh default dimensions for the node type
    const defaultDimensions = this.getDefaultDimensionsForType(nodeType);

    // Use default dimensions as a base, but allow override if explicitly provided with valid values
    if (nodeData.dimensions &&
        nodeData.dimensions.width !== undefined && !isNaN(nodeData.dimensions.width) && isFinite(nodeData.dimensions.width) &&
        nodeData.dimensions.height !== undefined && !isNaN(nodeData.dimensions.height) && isFinite(nodeData.dimensions.height) &&
        nodeData.dimensions.width > 0 && nodeData.dimensions.height > 0) {
      // Keep the provided dimensions as they are valid
      console.log(`Using provided dimensions for node (${nodeData.dimensions.width}x${nodeData.dimensions.height})`);
    } else {
      // Use default dimensions for the node type
      nodeData.dimensions = defaultDimensions;
      console.log(`Using default dimensions for node type ${nodeType}: ${defaultDimensions.width}x${defaultDimensions.height}`);
    }

    // Create default segments based on node type
    const defaultSegments = this.createDefaultSegmentsForType(nodeType);

    // Merge defaults with the provided data
    const mergedData = {
      ...defaultNodeData,
      ...nodeData,
      segments: nodeData.segments || defaultSegments
    };

    // Create batch command
    const batch = commandManager.startBatch(`Create node "${mergedData.title}"`);

    // Define command for adding the node
    batch.addCommand({
      execute: () => {
        return nodeStore.addNode(mergedData as NodeData).id;
      },
      undo: () => {
        nodeStore.removeNode(mergedData.id as string);
      },
      redo: () => {
        nodeStore.addNode(mergedData as NodeData);
      },
      description: `Create node "${mergedData.title}"`
    });

    // End batch
    commandManager.endBatch();

    console.log(`Created node with ID: ${mergedData.id}, Position: {x: ${mergedData.position?.x}, y: ${mergedData.position?.y}}, Dimensions: ${mergedData.dimensions?.width}x${mergedData.dimensions?.height}`);

    return mergedData.id as string;
  }

  /**
   * Create default segments based on node type
   * @param type Node type
   * @returns Array of default segments for the node type
   */
  private createDefaultSegmentsForType(type?: string): any[] {
    const validTypes = ['title', 'table', 'image', 'deadline', 'tasks', 'richtext', 'reminder'];
    const validType = type && validTypes.includes(type) ? type : 'richtext';

    const segmentId = Date.now() + Math.floor(Math.random() * 1000);

    switch (validType) {
      case 'title':
        return [{
          id: segmentId,
          type: 'title',
          content: 'Title Node'
        }];

      case 'tasks':
        return [{
          id: segmentId,
          type: 'tasks',
          title: 'Checklist',
          tasks: []
        }];

      case 'deadline':
        return [{
          id: segmentId,
          type: 'deadline',
          deadline: { date: null, description: '' }
        }];

      case 'image':
        return [{
          id: segmentId,
          type: 'image',
          imageData: {
            url: '',
            caption: 'Image',
            width: 0,
            height: 0
          }
        }];

      case 'reminder':
        return [{
          id: segmentId,
          type: 'reminder',
          reminder: {
            id: segmentId.toString(),
            title: 'Reminder',
            description: '',
            dueDate: null,
            completed: false,
            priority: 'medium',
            tags: []
          }
        }];

      case 'table':
        return [{
          id: segmentId,
          type: 'table',
          config: {
            rows: 3,
            columns: 3,
            data: Array(3).fill(null).map(() => Array(3).fill(''))
          }
        }];

      case 'richtext':
      default:
        return [{
          id: segmentId,
          type: 'richtext',
          content: ''
        }];
    }
  }

  /**
   * Get default dimensions based on node type
   * @param type Node type
   * @returns Default dimensions object
   */
  private getDefaultDimensionsForType(type?: string): NodeDimensions {
    // First validate the type parameter
    const validTypes = ['title', 'table', 'image', 'deadline', 'tasks', 'richtext', 'reminder'];
    const validType = type && validTypes.includes(type) ? type : 'richtext';

    // Return appropriate dimensions based on validated type
    switch (validType) {
      case 'title':
        return { width: 450, height: 80 };
      case 'table':
        return { width: 500, height: 400 };
      case 'image':
        return { width: 400, height: 300 };
      case 'deadline':
        return { width: 336, height: 200 };
      case 'tasks':
        return { width: 336, height: 356 };
      case 'richtext':
        return { width: 336, height: 356 };
      case 'reminder':
        return { width: 336, height: 200 };
      default:
        // Should never reach here due to validation above, but as a fallback
        return { width: 336, height: 356 };
    }
  }

  /**
   * Update a node
   * @param id The node ID
   * @param updates The updates to apply
   * @returns True if the node was updated, false if not found
   */
  updateNode(id: string, updates: Partial<NodeData>): boolean {
    const node = nodeStore.getNode(id);
    if (!node) return false;

    // Log the update operation
    console.log(`[NodeController] Updating node ${id} with:`, updates);

    // Validate updates to prevent accidental property loss
    if (updates.title === '') {
      console.warn(`[NodeController] Empty title detected, setting to 'Untitled'`);
      updates.title = 'Untitled';
    }

    // Ensure color is preserved if not explicitly updated
    if (updates.color === undefined && updates.segments !== undefined) {
      // This is likely a partial update for segments only
      console.log(`[NodeController] Preserving color for partial update`);
      // Don't add color to updates, let the existing color remain
    }

    // Store original values for undo
    const originalValues: Partial<NodeData> = {};

    // For each property in updates, store the original value
    Object.keys(updates).forEach(key => {
      if (key === 'position') {
        originalValues.position = { ...node.position };
      } else if (key === 'dimensions') {
        originalValues.dimensions = { ...node.dimensions };
      } else if (key === 'relationships') {
        originalValues.relationships = {
          parentId: node.relationships.parentId,
          childIds: [...node.relationships.childIds]
        };
      } else {
        // Use type assertion for dynamic property access
        (originalValues as any)[key] = (node as any)[key];
      }
    });

    // Check if this is a partial update (just segments and dimensions)
    const isPartialUpdate =
      updates.segments !== undefined &&
      updates.dimensions !== undefined &&
      Object.keys(updates).length <= 3; // segments, dimensions, and possibly type

    if (isPartialUpdate) {
      console.log(`[NodeController] Detected partial update for node ${id}, preserving other properties`);

      // For partial updates, ensure we're not accidentally resetting properties
      // by only updating the specified properties
      const safeUpdates = { ...updates };

      // Remove any undefined properties to avoid resetting them
      Object.keys(safeUpdates).forEach(key => {
        if (safeUpdates[key as keyof NodeData] === undefined) {
          delete safeUpdates[key as keyof NodeData];
        }
      });

      // Create command for partial update
      const command = {
        execute: () => {
          return nodeStore.updateNode(id, safeUpdates) !== undefined;
        },
        undo: () => {
          nodeStore.updateNode(id, originalValues);
        },
        redo: () => {
          nodeStore.updateNode(id, safeUpdates);
        },
        description: `Update segments for node "${node.title}"`
      };

      // Execute command
      return commandManager.execute(command);
    } else {
      // For full updates, proceed normally
      // Create command
      const command = {
        execute: () => {
          return nodeStore.updateNode(id, updates) !== undefined;
        },
        undo: () => {
          nodeStore.updateNode(id, originalValues);
        },
        redo: () => {
          nodeStore.updateNode(id, updates);
        },
        description: `Update node "${node.title}"`
      };

      // Execute command
      return commandManager.execute(command);
    }
  }

  /**
   * Delete a node
   * @param id The node ID
   * @returns True if the node was deleted, false if not found
   */
  async deleteNode(id: string): Promise<boolean> {
    // Get the node
    const node = nodeStore.getNode(id);
    if (!node) return false;

    // Check if this is a root node - don't allow deletion of root nodes
    if (!node.relationships.parentId) {
      console.warn('Cannot delete root node');
      return false;
    }

    // Get the current mouse position
    // If we have a window.event, use it, otherwise position in the center of the screen
    const mousePosition = {
      x: window.event ? (window.event as MouseEvent).clientX : window.innerWidth / 2,
      y: window.event ? (window.event as MouseEvent).clientY - 20 : window.innerHeight / 2 - 100
    };

    try {
      // Show the delete modal
      const hasChildren = node.relationships.childIds.length > 0;
      console.log(`Node ${id} (${node.title}) has children: ${hasChildren}, childIds:`, node.relationships.childIds);

      const result = await deleteModalService.showDeleteModal(
        id,
        node.title,
        hasChildren,
        mousePosition
      );

      // If the user cancelled, return false
      if (result === null) return false;

      // Use the enhanced deleteNodeWithOptions method with the user's choice
      return this.deleteNodeWithOptions(id, { deleteSubtree: result });
    } catch (error) {
      console.error('Error showing delete modal:', error);
      return false;
    }
  }

  /**
   * Delete a node with options
   * @param id The node ID
   * @param options Deletion options
   * @returns True if the node was deleted, false if not found
   */
  deleteNodeWithOptions(id: string, options: { deleteSubtree: boolean } = { deleteSubtree: false }): boolean {
    const node = nodeStore.getNode(id);
    if (!node) return false;

    // Check if this is a root node - don't allow deletion of root nodes
    if (!node.relationships.parentId) {
      console.warn('Cannot delete root node');
      return false;
    }

    // Store node data for undo
    const originalNodeData = node.toJSON();

    // Create batch command
    const batch = commandManager.startBatch(`Delete node "${node.title}"`);

    if (options.deleteSubtree) {
      // Delete the node and all its descendants
      this.deleteNodeAndDescendants(id, batch);
    } else {
      // Delete just the node and relink its children to its parent
      this.deleteNodeOnly(id, batch);
    }

    // End batch
    commandManager.endBatch();

    return true;
  }

  /**
   * Delete a node and all its descendants
   * @param id The node ID
   * @param batch The batch command
   */
  private deleteNodeAndDescendants(id: string, batch: BatchCommand): void {
    const node = nodeStore.getNode(id);
    if (!node) return;

    // Store node data for undo
    const nodeData = node.toJSON();
    const parentId = node.relationships.parentId;

    // Find connected edges
    const edges: EdgeData[] = [];
    nodeStore.getAllEdges().forEach(edge => {
      if (edge.source === id || edge.target === id) {
        edges.push(edge.toJSON());
      }
    });

    // Recursively delete all descendants
    const childIds = [...node.relationships.childIds];
    childIds.forEach(childId => {
      this.deleteNodeAndDescendants(childId, batch);
    });

    // Delete the node
    batch.addCommand({
      execute: () => {
        // If the node has a parent, update the parent's children
        if (parentId) {
          const parent = nodeStore.getNode(parentId);
          if (parent) {
            parent.removeChild(id);
          }
        }

        return nodeStore.removeNode(id);
      },
      undo: () => {
        // Restore the node
        nodeStore.addNode(nodeData);

        // Restore edges
        edges.forEach(edgeData => {
          nodeStore.addEdge(edgeData);
        });

        // Restore parent-child relationship
        if (parentId) {
          const parent = nodeStore.getNode(parentId);
          if (parent) {
            parent.addChild(id);
          }
        }
      },
      redo: () => {
        // If the node has a parent, update the parent's children
        if (parentId) {
          const parent = nodeStore.getNode(parentId);
          if (parent) {
            parent.removeChild(id);
          }
        }

        nodeStore.removeNode(id);
      },
      description: `Delete node "${node.title}" and its subtree`
    });
  }

  /**
   * Delete a node and relink its children to its parent
   * @param id The node ID
   * @param batch The batch command
   */
  private deleteNodeOnly(id: string, batch: BatchCommand): void {
    const node = nodeStore.getNode(id);
    if (!node) return;

    // Store node data for undo
    const nodeData = node.toJSON();
    const parentId = node.relationships.parentId;
    const childIds = [...node.relationships.childIds];

    // Find connected edges
    const edges: EdgeData[] = [];
    nodeStore.getAllEdges().forEach(edge => {
      if (edge.source === id || edge.target === id) {
        edges.push(edge.toJSON());
      }
    });

    // Store child-to-parent edges for undo
    const childToParentEdges: EdgeData[] = [];

    // Delete the node
    batch.addCommand({
      execute: () => {
        // Relink children to parent
        if (parentId) {
          const parent = nodeStore.getNode(parentId);

          childIds.forEach(childId => {
            const child = nodeStore.getNode(childId);
            if (child) {
              // Update child's parent
              child.setParent(parentId);

              // Add child to parent's children
              if (parent) {
                parent.addChild(childId);
              }

              // Create new edge from parent to child
              const edgeId = `${parentId}-${childId}`;
              const newEdge: EdgeData = {
                id: edgeId,
                source: parentId,
                target: childId
              };

              // Store the new edge for undo
              childToParentEdges.push(newEdge);

              // Add the edge
              nodeStore.addEdge(newEdge);
            }
          });
        } else {
          // If no parent, make children root nodes
          childIds.forEach(childId => {
            const child = nodeStore.getNode(childId);
            if (child) {
              child.setParent(undefined);
            }
          });
        }

        return nodeStore.removeNode(id);
      },
      undo: () => {
        // Restore the node
        nodeStore.addNode(nodeData);

        // Restore original edges
        edges.forEach(edgeData => {
          nodeStore.addEdge(edgeData);
        });

        // Remove child-to-parent edges
        childToParentEdges.forEach(edgeData => {
          nodeStore.removeEdge(edgeData.id);
        });

        // Restore parent-child relationships
        childIds.forEach(childId => {
          const child = nodeStore.getNode(childId);
          if (child) {
            child.setParent(id);

            // If there was a parent, remove child from parent's children
            if (parentId) {
              const parent = nodeStore.getNode(parentId);
              if (parent) {
                parent.removeChild(childId);
              }
            }
          }
        });

        // Restore node's parent relationship
        if (parentId) {
          const parent = nodeStore.getNode(parentId);
          if (parent) {
            parent.addChild(id);
          }
        }
      },
      redo: () => {
        // Relink children to parent
        if (parentId) {
          const parent = nodeStore.getNode(parentId);

          childIds.forEach(childId => {
            const child = nodeStore.getNode(childId);
            if (child) {
              // Update child's parent
              child.setParent(parentId);

              // Add child to parent's children
              if (parent) {
                parent.addChild(childId);
              }

              // Create new edge from parent to child
              const edgeId = `${parentId}-${childId}`;
              const newEdge: EdgeData = {
                id: edgeId,
                source: parentId,
                target: childId
              };

              // Add the edge
              nodeStore.addEdge(newEdge);
            }
          });
        } else {
          // If no parent, make children root nodes
          childIds.forEach(childId => {
            const child = nodeStore.getNode(childId);
            if (child) {
              child.setParent(undefined);
            }
          });
        }

        return nodeStore.removeNode(id);
      },
      description: `Delete node "${node.title}" and relink children`
    });
  }

  /**
   * Add a child node to a parent node
   * @param parentId The parent node ID
   * @param nodeData The child node data (partial)
   * @returns The ID of the created child node, or null if the parent wasn't found
   */
  addChildNode(parentId: string, nodeData: Partial<NodeData> = {}): string | null {
    const parentNode = nodeStore.getNode(parentId);
    if (!parentNode) return null;

    // Create a copy of the node data to avoid modifying the original
    const childData = { ...nodeData };

    // Get parent's effective color for inheritance
    const parentColor = parentNode.useCustomColor ?
      parentNode.color :
      (parentNode.inheritedColor || parentNode.color);

    // Set up color inheritance by default
    if (!childData.color) {
      childData.color = parentColor;
    }
    // Set useCustomColor to false by default to inherit parent color
    childData.useCustomColor = false;
    // Store the inherited color
    childData.inheritedColor = parentColor;

    // Ensure the node has a parent relationship
    childData.relationships = {
      ...(childData.relationships || {}),
      parentId,
      childIds: childData.relationships?.childIds || []
    };

    // Get node type or use default
    const nodeType = childData.type || 'richtext';

    // Create default segments based on node type if not provided
    if (!childData.segments) {
      childData.segments = this.createDefaultSegmentsForType(nodeType);
    }

    // Always get fresh default dimensions for the node type
    const defaultDimensions = this.getDefaultDimensionsForType(nodeType);

    // Use default dimensions as a base, but allow override if explicitly provided with valid values
    if (childData.dimensions &&
        childData.dimensions.width !== undefined && !isNaN(childData.dimensions.width) && isFinite(childData.dimensions.width) &&
        childData.dimensions.height !== undefined && !isNaN(childData.dimensions.height) && isFinite(childData.dimensions.height) &&
        childData.dimensions.width > 0 && childData.dimensions.height > 0) {
      // Keep the provided dimensions as they are valid
      console.log(`Using provided dimensions for child node (${childData.dimensions.width}x${childData.dimensions.height})`);
    } else {
      // Use default dimensions for the node type
      childData.dimensions = defaultDimensions;
      console.log(`Using default dimensions for child node type ${nodeType}: ${defaultDimensions.width}x${defaultDimensions.height}`);
    }

    // Validate and set position
    if (!childData.position ||
        childData.position.x === undefined || isNaN(childData.position.x) || !isFinite(childData.position.x) ||
        childData.position.y === undefined || isNaN(childData.position.y) || !isFinite(childData.position.y)) {

      // Ensure parent position is also valid, otherwise use default
      let parentX = 0;
      let parentY = 0;

      if (parentNode.position &&
          !isNaN(parentNode.position.x) && isFinite(parentNode.position.x) &&
          !isNaN(parentNode.position.y) && isFinite(parentNode.position.y)) {
        parentX = parentNode.position.x;
        parentY = parentNode.position.y;
      }

      // Calculate a better initial position relative to parent
      childData.position = {
        x: parentX + 100, // Offset horizontally to avoid overlap
        y: parentY + 100  // Offset vertically to avoid overlap
      };
      console.log(`Setting initial child node position relative to parent: {x: ${childData.position.x}, y: ${childData.position.y}}`);
    }

    // Generate hierarchical ID if not provided
    if (!childData.hierarchicalId || childData.hierarchicalId === '0-temp') {
      const childIndex = parentNode.relationships.childIds.length;
      const parentHierarchicalId = parentNode.hierarchicalId || '0';
      childData.hierarchicalId = generateHierarchicalId(parentHierarchicalId, childIndex);
    }

    // Generate an immutable node ID if not provided
    if (!childData.id) {
      // Get the current project ID
      const projectId = projectController.getCurrentProject()?.id;

      // If we have a project ID, use it to generate a node ID
      if (projectId) {
        childData.id = generateNodeId(projectId, childData.hierarchicalId);
      } else {
        // If no project ID is available (e.g., in demo mode), generate a temporary ID
        childData.id = generateTemporaryNodeId(childData.hierarchicalId);
      }

      console.log(`Generated immutable node ID for child: ${childData.id} with hierarchical ID: ${childData.hierarchicalId}`);
    }

    // If label is not specified, generate it from hierarchical ID
    if (!childData.label) {
      childData.label = hierarchicalIdToLabel(childData.hierarchicalId || '');
    }

    console.log(`Creating child node with ID: ${childData.id}, Label: ${childData.label}, Position: {x: ${childData.position.x}, y: ${childData.position.y}}, Dimensions: ${childData.dimensions.width}x${childData.dimensions.height}`);

    // Create batch command
    const batch = commandManager.startBatch(`Add child node to "${parentNode.title}"`);

    // Create the child node
    const childId = this.createNode(childData);

    // Create edge
    const edgeId = `${parentId}-${childId}`;
    const edgeData: EdgeData = {
      id: edgeId,
      source: parentId,
      target: childId
    };

    // Add edge
    batch.addCommand({
      execute: () => {
        return nodeStore.addEdge(edgeData);
      },
      undo: () => {
        nodeStore.removeEdge(edgeId);
      },
      redo: () => {
        nodeStore.addEdge(edgeData);
      },
      description: `Create edge from "${parentNode.title}" to child`
    });

    // End batch
    commandManager.endBatch();

    // Rebuild hierarchical IDs to ensure consistency
    this.rebuildAllHierarchicalIds();

    // Apply layout to ensure proper positioning, but skip view centering
    // This prevents the view from resetting when adding a new node
    layoutController.applyLayout(false, true);

    return childId;
  }

  /**
   * Move a node
   * @param id The node ID
   * @param position The new position in canvas coordinates (not screen coordinates)
   * @param isUserAction Whether this is a user-initiated action (default: true)
   * @returns True if the node was moved, false if not found
   */
  moveNode(id: string, position: { x: number; y: number }, isUserAction: boolean = true): boolean {
    // Throttle logging to avoid console spam
    const shouldLog = Date.now() % 5 === 0; // Only log ~20% of calls

    if (shouldLog) {
      console.log(`[DragProtocol] 17. NodeController.moveNode called for node ${id} to canvas position (${position.x.toFixed(2)}, ${position.y.toFixed(2)})`);
      console.log(`[CoordinateDebug] NodeController received canvas position: (${position.x.toFixed(2)}, ${position.y.toFixed(2)})`);
    }

    // Get the current layout type from the layout controller
    const isCustomLayout = layoutController.getCurrentLayout() === 'custom';

    if (shouldLog) {
      console.log(`[DragProtocol] 18. Current layout is ${isCustomLayout ? 'custom' : 'standard'}`);
    }

    if (isUserAction) {
      // If this is a user-initiated action and we're dragging, update the drag position
      const isDragging = mvcGraphLayoutService.isDraggingNode(id);

      if (shouldLog) {
        console.log(`[DragProtocol] 19. Is node ${id} being dragged? ${isDragging}`);
      }

      if (isDragging) {
        // Update the drag position
        if (shouldLog) {
          console.log(`[DragProtocol] 20. Updating drag position for node ${id} to canvas position (${position.x.toFixed(2)}, ${position.y.toFixed(2)})`);
        }

        try {
          mvcGraphLayoutService.updateDragPosition(position);

          if (shouldLog) {
            console.log(`[DragProtocol] 21. Successfully updated drag position in MVCGraphLayoutService`);
          }
        } catch (error) {
          console.error(`[DragProtocol] 22. ERROR: Failed to update drag position: ${error}`);
        }

        // For custom layouts, we also update the node's position directly
        if (isCustomLayout) {
          if (shouldLog) {
            console.log(`[DragProtocol] 23. Custom layout - updating node position directly in NodeStore`);
          }

          return this.updateNode(id, {
            position,
            manuallyPositioned: true
          });
        }

        // For standard layouts, we don't update the node's position directly
        // The layout worker will handle it based on the dragging state
        if (shouldLog) {
          console.log(`[DragProtocol] 23. Standard layout - letting layout worker handle position`);
        }

        return true;
      }
    }

    // If not dragging or not a user action, update the node's position directly
    if (shouldLog) {
      console.log(`[DragProtocol] 24. Not dragging or not user action - updating node position directly`);
    }

    return this.updateNode(id, {
      position,
      manuallyPositioned: isUserAction && isCustomLayout
    });
  }

  /**
   * Start dragging a node
   * @param id The node ID
   * @param position The initial drag position in canvas coordinates (not screen coordinates)
   * @param isCustomLayout Whether the current layout is custom (optional)
   * @returns True if dragging started, false if the node wasn't found
   */
  startDragging(id: string, position: { x: number; y: number }, isCustomLayout?: boolean): boolean {
    console.log(`[DragProtocol] 11. NodeController.startDragging called for node ${id} at canvas position (${position.x.toFixed(2)}, ${position.y.toFixed(2)})`);

    const node = nodeStore.getNode(id);
    if (!node) {
      console.error(`[DragProtocol] 12. ERROR: Node ${id} not found in nodeStore`);
      return false;
    }

    // If isCustomLayout is not provided, get it from the layout controller
    if (isCustomLayout === undefined) {
      isCustomLayout = layoutController.getCurrentLayout() === 'custom';
    }
    console.log(`[DragProtocol] 13. Layout type confirmed: ${isCustomLayout ? 'custom' : 'standard'}`);

    // Set the node as being dragged
    try {
      console.log(`[DragProtocol] 14. Calling mvcGraphLayoutService.setDraggingNode for node ${id}`);
      mvcGraphLayoutService.setDraggingNode(id, position, isCustomLayout);
      console.log(`[DragProtocol] 15. Successfully set dragging node ${id} in MVCGraphLayoutService`);
    } catch (error) {
      console.error(`[DragProtocol] 16. ERROR: Failed to set dragging node: ${error}`);
      return false;
    }

    return true;
  }

  /**
   * End dragging a node
   * @param savePosition Whether to save the final position (true for custom layouts)
   * @returns True if dragging ended, false if no node was being dragged
   */
  endDragging(savePosition?: boolean): boolean {
    console.log(`[DragProtocol] 25. NodeController.endDragging called, savePosition: ${savePosition}`);

    // Get the current layout type from the layout controller
    const isCustomLayout = layoutController.getCurrentLayout() === 'custom';
    console.log(`[DragProtocol] 26. Current layout is ${isCustomLayout ? 'custom' : 'standard'}`);

    // If savePosition is not specified, use the layout type
    const shouldSavePosition = savePosition !== undefined ? savePosition : isCustomLayout;
    console.log(`[DragProtocol] 27. shouldSavePosition: ${shouldSavePosition}`);

    // Get dragging info before clearing
    const dragInfoBefore = mvcGraphLayoutService.getDraggingInfo();
    console.log(`[DragProtocol] 28. Dragging info before clearing: ${JSON.stringify(dragInfoBefore)}`);

    // Clear the dragging state
    try {
      console.log(`[DragProtocol] 29. Calling mvcGraphLayoutService.clearDraggingNode(${shouldSavePosition})`);
      const dragInfo = mvcGraphLayoutService.clearDraggingNode(shouldSavePosition);

      if (!dragInfo) {
        console.error(`[DragProtocol] 30. ERROR: No drag info returned when ending drag`);
        return false;
      }

      console.log(`[DragProtocol] 30. Successfully ended dragging for node ${dragInfo.nodeId}`);

      // If we should save the position, update the node's position directly
      if (shouldSavePosition) {
        console.log(`[DragProtocol] 31. Saving final position (${dragInfo.position.x}, ${dragInfo.position.y}) for node ${dragInfo.nodeId}`);
        const updateResult = this.updateNode(dragInfo.nodeId, {
          position: dragInfo.position,
          manuallyPositioned: true
        });
        console.log(`[DragProtocol] 32. Node position update ${updateResult ? 'succeeded' : 'failed'}`);
        return updateResult;
      }

      console.log(`[DragProtocol] 31. Not saving position, letting layout worker handle final positioning`);
      return true;
    } catch (error) {
      console.error(`[DragProtocol] 33. ERROR: Failed to end dragging: ${error}`);
      return false;
    }
  }

  /**
   * Check if a node is currently being dragged
   * @param nodeId The node ID to check
   * @returns True if the node is being dragged
   */
  isDraggingNode(nodeId: string): boolean {
    return mvcGraphLayoutService.isDraggingNode(nodeId);
  }

  /**
   * Move a node for layout purposes without marking it as manually positioned
   * @param id The node ID
   * @param position The new position
   * @returns True if the node was moved, false if not found
   */
  moveNodeForLayout(id: string, position: { x: number; y: number }): boolean {
    console.log(`[NodeController] Moving node ${id} for layout to {x: ${position.x}, y: ${position.y}} and setting manuallyPositioned flag to false`);
    // Explicitly set manuallyPositioned to false to ensure the node can be repositioned by layout algorithms
    return this.updateNode(id, {
      position,
      manuallyPositioned: false
    });
  }

  /**
   * Resize a node
   * @param id The node ID
   * @param dimensions The new dimensions
   * @returns True if the node was resized, false if not found
   */
  resizeNode(id: string, dimensions: { width: number; height: number }): boolean {
    const result = this.updateNode(id, { dimensions });

    // If the resize was successful and the node has children, recalculate layout
    if (result) {
      const node = nodeStore.getNode(id);
      if (node && node.relationships.childIds.length > 0) {
        console.log(`[NodeController] Node ${id} was resized and has ${node.relationships.childIds.length} children, recalculating layout`);

        // Use the dedicated method for handling node resizing
        // Pass true to skip view centering
        layoutController.recalculateAfterResize(id, true);
      }
    }

    return result;
  }

  /**
   * Change a node's color
   * @param id The node ID
   * @param color The new color
   * @param useCustomColor Whether to use the custom color
   * @returns True if the node's color was changed, false if not found
   */
  changeNodeColor(id: string, color: string, useCustomColor: boolean = true): boolean {
    return this.updateNode(id, { color, useCustomColor });
  }

  /**
   * Show the color picker modal and update the node's color based on user selection
   * @param id The node ID
   * @param mousePosition The position to show the color picker at
   * @returns A promise that resolves when the color picker is closed
   */
  async showColorPicker(id: string, mousePosition: { x: number, y: number }): Promise<void> {
    const node = nodeStore.getNode(id);
    if (!node) return;

    try {
      // Define the immediate color change handler
      const handleImmediateColorChange = (color: string, useCustomColor: boolean) => {
        // Update the node with the new color
        this.updateNode(id, {
          color: color,
          useCustomColor: useCustomColor
        });

        // Always update children after a color change
        // If useCustomColor is true, children that inherit will use this new color
        // If useCustomColor is false, children that inherit will use the parent's color
        const effectiveColor = useCustomColor ? color : this.getParentColor(id);

        // Update all children that inherit colors
        this.updateChildrenColors(id, effectiveColor);
      };

      // Show the color picker modal with immediate color change handler
      await colorPickerService.showColorPicker(
        id,
        node.color,
        node.useCustomColor,
        mousePosition,
        handleImmediateColorChange
      );

    } catch (error) {
      console.error('Error showing color picker:', error);
    }
  }

  /**
   * Get the parent's color for a node
   * @param id The node ID
   * @returns The parent's effective color, or a default color if no parent exists
   */
  private getParentColor(id: string): string {
    const node = nodeStore.getNode(id);
    if (!node || !node.relationships.parentId) return '#4299e1'; // default color for root node

    // Get the parent node
    const parent = nodeStore.getNode(node.relationships.parentId);
    if (!parent) return '#4299e1';

    // Return the effective color of the parent (either custom or inherited)
    return parent.useCustomColor ? parent.color : (parent.inheritedColor || parent.color);
  }

  /**
   * Update the colors of all children that inherit from this node
   * @param nodeId The parent node ID
   * @param parentColor The parent's effective color
   */
  private updateChildrenColors(nodeId: string, parentColor: string): void {
    const node = nodeStore.getNode(nodeId);
    if (!node) return;

    // If node doesn't use custom color, update its inherited color
    if (!node.useCustomColor) {
      console.log(`Updating node ${nodeId} with inherited color ${parentColor}`);

      // Create a batch command for better performance with many updates
      const batch = commandManager.startBatch(`Update inherited colors`);

      // Update the node's color
      this.updateNode(nodeId, {
        color: parentColor,
        useCustomColor: false,
        inheritedColor: parentColor
      });

      // Update children recursively
      const childIds = node.relationships?.childIds ?? [];
      childIds.forEach((childId: string) => {
        this.updateChildrenColors(childId, parentColor);
      });

      // End the batch
      commandManager.endBatch();
    } else {
      // This node uses a custom color, but we still need to update its children
      // that might inherit colors
      const childIds = node.relationships?.childIds ?? [];
      childIds.forEach((childId: string) => {
        this.updateChildrenColors(childId, node.color);
      });
    }
  }

  /**
   * Toggle a node's completed state
   * @param id The node ID
   * @returns The new completed state, or null if the node wasn't found
   */
  toggleNodeCompleted(id: string): boolean | null {
    const node = nodeStore.getNode(id);
    if (!node) return null;

    const newCompleted = !node.completed;
    const success = this.updateNode(id, { completed: newCompleted });

    return success ? newCompleted : null;
  }

  /**
   * Toggle a node's collapsed state
   * @param id The node ID
   * @returns The new collapsed state, or null if the node wasn't found
   */
  toggleNodeCollapsed(id: string): boolean | null {
    const node = nodeStore.getNode(id);
    if (!node) return null;

    const newCollapsed = !node.collapsed;
    const success = this.updateNode(id, { collapsed: newCollapsed });

    return success ? newCollapsed : null;
  }

  /**
   * Add a tag to a node
   * @param nodeId The node ID
   * @param tagId The tag ID
   * @returns True if the tag was added, false if the node wasn't found
   */
  addTagToNode(nodeId: string, tagId: string): boolean {
    const node = nodeStore.getNode(nodeId);
    if (!node) return false;

    // Check if the node already has the tag
    const tags = node.tags;
    if (tags.includes(tagId)) return true;

    // Add the tag
    const newTags = [...tags, tagId];
    return this.updateNode(nodeId, { tags: newTags });
  }

  /**
   * Remove a tag from a node
   * @param nodeId The node ID
   * @param tagId The tag ID
   * @returns True if the tag was removed, false if the node wasn't found
   */
  removeTagFromNode(nodeId: string, tagId: string): boolean {
    const node = nodeStore.getNode(nodeId);
    if (!node) return false;

    // Check if the node has the tag
    const tags = node.tags;
    if (!tags.includes(tagId)) return true;

    // Remove the tag
    const newTags = tags.filter(id => id !== tagId);
    return this.updateNode(nodeId, { tags: newTags });
  }

  /**
   * Relink a node to a new parent
   * @param nodeId The node ID
   * @param newParentId The new parent node ID
   * @returns True if the node was relinked, false if either node wasn't found
   */
  relinkNode(nodeId: string, newParentId: string): boolean {
    // Use the enhanced relinkNodeWithOptions method with default options (node only)
    return this.relinkNodeWithOptions(nodeId, newParentId, { withSubtree: false });
  }

  /**
   * Relink a node to a new parent with options
   * @param nodeId The node ID
   * @param newParentId The new parent node ID
   * @param options Relinking options
   * @returns True if the node was relinked, false if either node wasn't found
   */
  relinkNodeWithOptions(nodeId: string, newParentId: string, options: { withSubtree: boolean } = { withSubtree: false }): boolean {
    console.log(`[RelinkOperation] Starting relinkNodeWithOptions: nodeId=${nodeId}, newParentId=${newParentId}, withSubtree=${options.withSubtree}`);

    const node = nodeStore.getNode(nodeId);
    const newParent = nodeStore.getNode(newParentId);

    if (!node || !newParent) {
      console.error(`[RelinkOperation] Node or new parent not found: node=${!!node}, newParent=${!!newParent}`);
      return false;
    }

    // Check if the node is already linked to the new parent
    if (node.relationships.parentId === newParentId) {
      console.log(`[RelinkOperation] Node is already linked to the new parent`);
      return true;
    }

    // Check if the new parent is a descendant of the node (would create a cycle)
    if (this.isDescendant(newParentId, nodeId)) {
      console.error(`[RelinkOperation] Cannot relink: would create a cycle`);
      return false;
    }

    // Store the old parent ID
    const oldParentId = node.relationships.parentId;

    // Create batch command
    const batchDescription = options.withSubtree
      ? `Relink node "${node.title}" and its subtree to "${newParent.title}"`
      : `Relink node "${node.title}" to "${newParent.title}"`;

    console.log(`[RelinkOperation] Creating batch command: ${batchDescription}`);
    const batch = commandManager.startBatch(batchDescription);

    console.log(`[RelinkOperation] Node details before relinking:`);
    console.log(`[RelinkOperation] - ID: ${nodeId}`);
    console.log(`[RelinkOperation] - Hierarchical ID: ${node.hierarchicalId}`);
    console.log(`[RelinkOperation] - Old Parent: ${oldParentId}`);
    console.log(`[RelinkOperation] - New Parent: ${newParentId}`);
    console.log(`[RelinkOperation] - With Subtree: ${options.withSubtree}`);
    console.log(`[RelinkOperation] - Children: ${node.relationships.childIds.join(', ')}`);

    if (options.withSubtree) {
      console.log(`[RelinkOperation] Relinking node with subtree`);
      // Update hierarchical IDs for the node and its descendants
      this.updateHierarchicalIdsForSubtree(nodeId, newParentId, oldParentId, batch);
    } else {
      console.log(`[RelinkOperation] Relinking node only`);
      // Update hierarchical ID for just the node, move children to old parent
      this.updateHierarchicalIdForNodeOnly(nodeId, newParentId, oldParentId, batch);
    }

    // Update parent-child relationships based on the new hierarchical IDs
    batch.addCommand({
      execute: () => {
        console.log(`[RelinkOperation] Executing updateRelationshipsFromHierarchicalIds`);
        // Update relationships based on new hierarchical IDs
        this.updateRelationshipsFromHierarchicalIds();
        return true;
      },
      undo: () => {
        console.log(`[RelinkOperation] Undoing updateRelationshipsFromHierarchicalIds`);
        // Restore relationships based on old hierarchical IDs
        this.updateRelationshipsFromHierarchicalIds();
      },
      redo: () => {
        console.log(`[RelinkOperation] Redoing updateRelationshipsFromHierarchicalIds`);
        // Update relationships based on new hierarchical IDs again
        this.updateRelationshipsFromHierarchicalIds();
      },
      description: `Update relationships based on new hierarchical IDs`
    });

    // End batch
    console.log(`[RelinkOperation] Ending batch command`);
    commandManager.endBatch();

    // Trigger layout recalculation, but skip view centering
    console.log(`[RelinkOperation] Triggering layout recalculation (skipping view centering)`);
    layoutController.applyLayout(false, true);

    // Validate the graph structure after relinking
    console.log(`[RelinkOperation] Validating graph structure after relinking`);
    this.validateGraphStructure();

    // Validate the relinking operation
    const isValid = this.validateRelinking(nodeId, newParentId);
    if (!isValid) {
      console.error(`[RelinkOperation] Relinking validation failed, attempting to fix...`);
      // Attempt to fix by rebuilding relationships from hierarchical IDs
      this.updateRelationshipsFromHierarchicalIds();
    }

    // Force save to ensure changes are persisted
    nodeStore.save().then(() => {
      console.log(`[RelinkOperation] Changes saved to database`);
    }).catch(error => {
      console.error(`[RelinkOperation] Failed to save changes to database:`, error);
    });

    console.log(`[RelinkOperation] Relinking operation completed successfully`);
    return true;
  }

  /**
   * Relink a node to a new parent, children stay with the node
   * @param nodeId The node ID
   * @param newParentId The new parent node ID
   * @param oldParentId The old parent node ID
   * @param batch The batch command
   */
  private relinkNodeWithSubtree(nodeId: string, newParentId: string, oldParentId: string | undefined, batch: BatchCommand): void {
    const node = nodeStore.getNode(nodeId);
    const newParent = nodeStore.getNode(newParentId);

    if (!node || !newParent) return;

    // If the node has an old parent, remove it from the old parent's children
    if (oldParentId) {
      const oldParent = nodeStore.getNode(oldParentId);
      if (oldParent) {
        // Remove the node from the old parent's children
        batch.addCommand({
          execute: () => {
            return this.updateNode(oldParentId, {
              relationships: {
                parentId: oldParent.relationships.parentId,
                childIds: oldParent.relationships.childIds.filter(id => id !== nodeId)
              }
            });
          },
          undo: () => {
            this.updateNode(oldParentId, {
              relationships: {
                parentId: oldParent.relationships.parentId,
                childIds: [...oldParent.relationships.childIds, nodeId]
              }
            });
          },
          redo: () => {
            this.updateNode(oldParentId, {
              relationships: {
                parentId: oldParent.relationships.parentId,
                childIds: oldParent.relationships.childIds.filter(id => id !== nodeId)
              }
            });
          },
          description: `Remove node "${node.title}" from parent "${oldParent.title}"`
        });

        // Remove the edge
        const oldEdgeId = `${oldParentId}-${nodeId}`;
        batch.addCommand({
          execute: () => {
            return nodeStore.removeEdge(oldEdgeId);
          },
          undo: () => {
            nodeStore.addEdge({
              id: oldEdgeId,
              source: oldParentId,
              target: nodeId
            });
          },
          redo: () => {
            nodeStore.removeEdge(oldEdgeId);
          },
          description: `Remove edge from "${oldParent.title}" to "${node.title}"`
        });
      }
    }

    // Add the node to the new parent's children
    batch.addCommand({
      execute: () => {
        return this.updateNode(newParentId, {
          relationships: {
            parentId: newParent.relationships.parentId,
            childIds: [...newParent.relationships.childIds, nodeId]
          }
        });
      },
      undo: () => {
        this.updateNode(newParentId, {
          relationships: {
            parentId: newParent.relationships.parentId,
            childIds: newParent.relationships.childIds.filter(id => id !== nodeId)
          }
        });
      },
      redo: () => {
        this.updateNode(newParentId, {
          relationships: {
            parentId: newParent.relationships.parentId,
            childIds: [...newParent.relationships.childIds, nodeId]
          }
        });
      },
      description: `Add node "${node.title}" to parent "${newParent.title}"`
    });

    // Update the node's parent
    batch.addCommand({
      execute: () => {
        return this.updateNode(nodeId, {
          relationships: {
            parentId: newParentId,
            childIds: node.relationships.childIds
          }
        });
      },
      undo: () => {
        this.updateNode(nodeId, {
          relationships: {
            parentId: oldParentId,
            childIds: node.relationships.childIds
          }
        });
      },
      redo: () => {
        this.updateNode(nodeId, {
          relationships: {
            parentId: newParentId,
            childIds: node.relationships.childIds
          }
        });
      },
      description: `Set parent of "${node.title}" to "${newParent.title}"`
    });

    // Add the edge
    const newEdgeId = `${newParentId}-${nodeId}`;
    batch.addCommand({
      execute: () => {
        return nodeStore.addEdge({
          id: newEdgeId,
          source: newParentId,
          target: nodeId
        });
      },
      undo: () => {
        nodeStore.removeEdge(newEdgeId);
      },
      redo: () => {
        nodeStore.addEdge({
          id: newEdgeId,
          source: newParentId,
          target: nodeId
        });
      },
      description: `Create edge from "${newParent.title}" to "${node.title}"`
    });

    // Update hierarchical IDs for the node and its descendants
    batch.addCommand({
      execute: () => {
        return this.updateHierarchicalIds(nodeId);
      },
      undo: () => {
        // If there was an old parent, restore the old hierarchical ID structure
        if (oldParentId) {
          this.updateHierarchicalIds(oldParentId);
        }
        // If there was no old parent, the node was a root node
        else {
          const node = nodeStore.getNode(nodeId);
          if (node) {
            node.hierarchicalId = '0';
            node.label = 'Root';

            // Update all children recursively
            node.relationships.childIds.forEach((childId, index) => {
              const childNode = nodeStore.getNode(childId);
              if (childNode) {
                const childHierarchicalId = generateHierarchicalId('0', index);
                this.updateHierarchicalIdRecursively(childNode, childHierarchicalId);
              }
            });
          }
        }
      },
      redo: () => {
        return this.updateHierarchicalIds(nodeId);
      },
      description: `Update hierarchical IDs for "${node.title}" and descendants`
    });
  }

  /**
   * Relink a node to a new parent, children go to original parent
   * @param nodeId The node ID
   * @param newParentId The new parent node ID
   * @param oldParentId The old parent node ID
   * @param batch The batch command
   */
  private relinkNodeOnly(nodeId: string, newParentId: string, oldParentId: string | undefined, batch: BatchCommand): void {
    const node = nodeStore.getNode(nodeId);
    const newParent = nodeStore.getNode(newParentId);

    if (!node || !newParent) return;

    // Store the child IDs for relinking
    const childIds = [...node.relationships.childIds];

    // If the node has an old parent, remove it from the old parent's children
    if (oldParentId) {
      const oldParent = nodeStore.getNode(oldParentId);
      if (oldParent) {
        // Remove the node from the old parent's children
        batch.addCommand({
          execute: () => {
            return this.updateNode(oldParentId, {
              relationships: {
                parentId: oldParent.relationships.parentId,
                childIds: oldParent.relationships.childIds.filter(id => id !== nodeId)
              }
            });
          },
          undo: () => {
            this.updateNode(oldParentId, {
              relationships: {
                parentId: oldParent.relationships.parentId,
                childIds: [...oldParent.relationships.childIds, nodeId]
              }
            });
          },
          redo: () => {
            this.updateNode(oldParentId, {
              relationships: {
                parentId: oldParent.relationships.parentId,
                childIds: oldParent.relationships.childIds.filter(id => id !== nodeId)
              }
            });
          },
          description: `Remove node "${node.title}" from parent "${oldParent.title}"`
        });

        // Remove the edge
        const oldEdgeId = `${oldParentId}-${nodeId}`;
        batch.addCommand({
          execute: () => {
            return nodeStore.removeEdge(oldEdgeId);
          },
          undo: () => {
            nodeStore.addEdge({
              id: oldEdgeId,
              source: oldParentId,
              target: nodeId
            });
          },
          redo: () => {
            nodeStore.removeEdge(oldEdgeId);
          },
          description: `Remove edge from "${oldParent.title}" to "${node.title}"`
        });

        // Relink children to old parent
        if (childIds.length > 0) {
          // Store original child-parent relationships for undo
          const originalChildRelationships = childIds.map(childId => {
            const child = nodeStore.getNode(childId);
            return {
              childId,
              parentId: child?.relationships.parentId
            };
          });

          batch.addCommand({
            execute: () => {
              // Add children to old parent's children
              const updatedOldParent = nodeStore.getNode(oldParentId);
              if (updatedOldParent) {
                childIds.forEach(childId => {
                  const child = nodeStore.getNode(childId);
                  if (child) {
                    // Update child's parent
                    child.setParent(oldParentId);

                    // Add child to old parent's children
                    updatedOldParent.addChild(childId);

                    // Create new edge from old parent to child
                    const edgeId = `${oldParentId}-${childId}`;
                    nodeStore.addEdge({
                      id: edgeId,
                      source: oldParentId,
                      target: childId
                    });
                  }
                });
              }
              return true;
            },
            undo: () => {
              // Restore original relationships
              originalChildRelationships.forEach(rel => {
                const child = nodeStore.getNode(rel.childId);
                if (child) {
                  // Remove from old parent
                  const oldParent = nodeStore.getNode(oldParentId);
                  if (oldParent) {
                    oldParent.removeChild(rel.childId);
                  }

                  // Remove edge
                  const edgeId = `${oldParentId}-${rel.childId}`;
                  nodeStore.removeEdge(edgeId);

                  // Restore original parent
                  child.setParent(rel.parentId);

                  // If original parent was the node being moved, add back to its children
                  if (rel.parentId === nodeId) {
                    const movedNode = nodeStore.getNode(nodeId);
                    if (movedNode) {
                      movedNode.addChild(rel.childId);
                    }
                  }
                }
              });
            },
            redo: () => {
              // Add children to old parent's children again
              const updatedOldParent = nodeStore.getNode(oldParentId);
              if (updatedOldParent) {
                childIds.forEach(childId => {
                  const child = nodeStore.getNode(childId);
                  if (child) {
                    // Update child's parent
                    child.setParent(oldParentId);

                    // Add child to old parent's children
                    updatedOldParent.addChild(childId);

                    // Create new edge from old parent to child
                    const edgeId = `${oldParentId}-${childId}`;
                    nodeStore.addEdge({
                      id: edgeId,
                      source: oldParentId,
                      target: childId
                    });
                  }
                });
              }
              return true;
            },
            description: `Relink children of "${node.title}" to its original parent`
          });
        }
      }
    }

    // Add the node to the new parent's children
    batch.addCommand({
      execute: () => {
        return this.updateNode(newParentId, {
          relationships: {
            parentId: newParent.relationships.parentId,
            childIds: [...newParent.relationships.childIds, nodeId]
          }
        });
      },
      undo: () => {
        this.updateNode(newParentId, {
          relationships: {
            parentId: newParent.relationships.parentId,
            childIds: newParent.relationships.childIds.filter(id => id !== nodeId)
          }
        });
      },
      redo: () => {
        this.updateNode(newParentId, {
          relationships: {
            parentId: newParent.relationships.parentId,
            childIds: [...newParent.relationships.childIds, nodeId]
          }
        });
      },
      description: `Add node "${node.title}" to parent "${newParent.title}"`
    });

    // Update the node's parent and clear its children
    batch.addCommand({
      execute: () => {
        return this.updateNode(nodeId, {
          relationships: {
            parentId: newParentId,
            childIds: [] // Clear children since they're now linked to the old parent
          }
        });
      },
      undo: () => {
        this.updateNode(nodeId, {
          relationships: {
            parentId: oldParentId,
            childIds: childIds // Restore original children
          }
        });
      },
      redo: () => {
        this.updateNode(nodeId, {
          relationships: {
            parentId: newParentId,
            childIds: [] // Clear children again
          }
        });
      },
      description: `Set parent of "${node.title}" to "${newParent.title}" and clear its children`
    });

    // Add the edge
    const newEdgeId = `${newParentId}-${nodeId}`;
    batch.addCommand({
      execute: () => {
        return nodeStore.addEdge({
          id: newEdgeId,
          source: newParentId,
          target: nodeId
        });
      },
      undo: () => {
        nodeStore.removeEdge(newEdgeId);
      },
      redo: () => {
        nodeStore.addEdge({
          id: newEdgeId,
          source: newParentId,
          target: nodeId
        });
      },
      description: `Create edge from "${newParent.title}" to "${node.title}"`
    });

    // Update hierarchical IDs for the node and the old parent (for its new children)
    batch.addCommand({
      execute: () => {
        // Update the node's hierarchical ID
        this.updateHierarchicalIds(nodeId);

        // If there was an old parent, update its hierarchical IDs too (for the relinked children)
        if (oldParentId) {
          this.updateHierarchicalIds(oldParentId);
        }
        return true;
      },
      undo: () => {
        // If there was an old parent, restore the old hierarchical ID structure
        if (oldParentId) {
          this.updateHierarchicalIds(oldParentId);
        }

        // Restore the node's hierarchical ID
        const node = nodeStore.getNode(nodeId);
        if (node) {
          if (!oldParentId) {
            // If there was no old parent, the node was a root node
            node.hierarchicalId = '0';
            node.label = 'Root';
          } else {
            this.updateHierarchicalIds(nodeId);
          }
        }
      },
      redo: () => {
        // Update the node's hierarchical ID
        this.updateHierarchicalIds(nodeId);

        // If there was an old parent, update its hierarchical IDs too (for the relinked children)
        if (oldParentId) {
          this.updateHierarchicalIds(oldParentId);
        }
        return true;
      },
      description: `Update hierarchical IDs for "${node.title}" and affected nodes`
    });
  }

  /**
   * Check if a node is a descendant of another node using hierarchical IDs
   * @param nodeId The node ID
   * @param ancestorId The potential ancestor node ID
   * @returns True if the node is a descendant of the ancestor
   */
  isDescendant(nodeId: string, ancestorId: string): boolean {
    // Get the potential ancestor node
    const ancestorNode = nodeStore.getNode(ancestorId);
    if (!ancestorNode || !ancestorNode.hierarchicalId) {
      console.log(`[RelinkOperation] Cannot check descendant: ancestor node ${ancestorId} not found or has no hierarchical ID`);
      return false;
    }

    // Get the node
    const node = nodeStore.getNode(nodeId);
    if (!node || !node.hierarchicalId) {
      console.log(`[RelinkOperation] Cannot check descendant: node ${nodeId} not found or has no hierarchical ID`);
      return false;
    }

    // If they're the same node, it's not a descendant
    if (node.hierarchicalId === ancestorNode.hierarchicalId) {
      return false;
    }

    // Check if ancestor's hierarchical ID is a prefix of node's hierarchical ID
    // We need to ensure it's a proper prefix by checking for the dash
    // For example, "0-1" is a prefix of "0-1-2" but not of "0-12"
    const isDescendant = node.hierarchicalId.startsWith(ancestorNode.hierarchicalId + '-');

    if (isDescendant) {
      console.log(`[RelinkOperation] Cycle detection: ${nodeId} is a descendant of ${ancestorId}`);
      console.log(`[RelinkOperation] Node hierarchicalId: ${node.hierarchicalId}, Ancestor hierarchicalId: ${ancestorNode.hierarchicalId}`);
    }

    return isDescendant;
  }

  /**
   * Validate a relinking operation
   * @param nodeId The node ID that was relinked
   * @param newParentId The new parent node ID
   * @returns True if the validation passed, false otherwise
   */
  private validateRelinking(nodeId: string, newParentId: string): boolean {
    console.log(`[RelinkOperation] Validating relinking of node ${nodeId} to parent ${newParentId}`);

    // Get the node and its new parent
    const node = nodeStore.getNode(nodeId);
    const newParent = nodeStore.getNode(newParentId);

    if (!node || !newParent) {
      console.error(`[RelinkOperation] Validation failed: node or new parent not found`);
      return false;
    }

    // Check that the node's parentId is set to the new parent
    if (node.relationships.parentId !== newParentId) {
      console.error(`[RelinkOperation] Validation failed: node's parentId is not set to the new parent`);
      return false;
    }

    // Check that the node is in the new parent's childIds
    if (!newParent.relationships.childIds.includes(nodeId)) {
      console.error(`[RelinkOperation] Validation failed: node is not in new parent's childIds`);
      return false;
    }

    // Check that the edge exists
    const edgeId = `${newParentId}-${nodeId}`;
    const edge = nodeStore.getEdge(edgeId);
    if (!edge) {
      console.error(`[RelinkOperation] Validation failed: edge from new parent to node does not exist`);
      return false;
    }

    // Check hierarchical ID consistency
    const expectedPrefix = newParent.hierarchicalId === '0' ? '0-' : `${newParent.hierarchicalId}-`;
    if (!node.hierarchicalId || !node.hierarchicalId.startsWith(expectedPrefix)) {
      console.error(`[RelinkOperation] Validation failed: node's hierarchicalId does not have the expected prefix`);
      return false;
    }

    console.log(`[RelinkOperation] Validation passed for relinking of node ${nodeId} to parent ${newParentId}`);
    return true;
  }

  /**
   * Update all references to a node ID in the system
   * @param oldId The old node ID
   * @param newId The new node ID
   * @deprecated This method is no longer needed with immutable node IDs
   */
  private updateNodeReferences(oldId: string, newId: string): void {
    console.log(`[RelinkOperation] DEPRECATED: Updating references from ${oldId} to ${newId}`);
    console.log(`[RelinkOperation] This method should not be called with immutable node IDs`);

    // With immutable node IDs, we should never need to update references
    // This method is kept for backward compatibility but should not be used
  }

  /**
   * Update hierarchical IDs for a node and all its descendants
   * @param nodeId The node ID
   * @returns True if the update was successful, false if the node wasn't found
   */
  updateHierarchicalIds(nodeId: string): boolean {
    const node = nodeStore.getNode(nodeId);
    if (!node) return false;

    // If this is a root node, set hierarchical ID to '0'
    if (!node.relationships.parentId) {
      const newHierarchicalId = '0';

      // Update hierarchical ID only, not the node ID
      node.hierarchicalId = newHierarchicalId;

      // Update the label to match the hierarchical ID
      node.label = hierarchicalIdToLabel(newHierarchicalId);

      console.log(`Updated root node ${nodeId} hierarchical ID to '0', label to 'Root'`);
    } else {
      // For child nodes, get the parent's hierarchical ID and this node's index among siblings
      const parentNode = nodeStore.getNode(node.relationships.parentId);
      if (!parentNode) return false;

      const childIndex = parentNode.relationships.childIds.indexOf(nodeId);
      if (childIndex === -1) return false;

      const parentHierarchicalId = parentNode.hierarchicalId || '0';
      const newHierarchicalId = generateHierarchicalId(parentHierarchicalId, childIndex);

      // Update hierarchical ID only, not the node ID
      node.hierarchicalId = newHierarchicalId;

      // Update the label to match the hierarchical ID
      node.label = hierarchicalIdToLabel(newHierarchicalId);

      console.log(`Updated node ${nodeId} hierarchical ID to '${newHierarchicalId}', label to '${hierarchicalIdToLabel(newHierarchicalId)}'`);
    }

    // Recursively update all children
    for (const childId of node.relationships.childIds) {
      this.updateHierarchicalIds(childId);
    }

    return true;
  }

  /**
   * Update hierarchical IDs for a node and its subtree when relinking
   * @param nodeId The node ID
   * @param newParentId The new parent node ID
   * @param oldParentId The old parent node ID
   * @param batch The batch command
   */
  private updateHierarchicalIdsForSubtree(nodeId: string, newParentId: string, oldParentId: string | undefined, batch: BatchCommand): void {
    const node = nodeStore.getNode(nodeId);
    const newParent = nodeStore.getNode(newParentId);

    if (!node || !newParent) return;

    // Store the original hierarchical IDs for undo
    const originalNodeHierarchicalId = node.hierarchicalId || '';

    // Get all descendants to store their original hierarchical IDs
    const descendants = this.getAllDescendants(nodeId);
    const originalDescendantHierarchicalIds = new Map<string, string>();

    // Store original hierarchical IDs for all descendants
    descendants.forEach(descendant => {
      originalDescendantHierarchicalIds.set(descendant.id, descendant.hierarchicalId || '');
    });

    // Add command to update the node's parent relationship
    batch.addCommand({
      execute: () => {
        // Update the node's parent relationship
        if (oldParentId) {
          const oldParent = nodeStore.getNode(oldParentId);
          if (oldParent) {
            // Remove node from old parent's children
            oldParent.removeChild(nodeId);
          }
        }

        // Add node to new parent's children
        newParent.addChild(nodeId);

        // Update the node's parent reference
        node.setParent(newParentId);

        return true;
      },
      undo: () => {
        // Restore the node's parent relationship
        newParent.removeChild(nodeId);

        if (oldParentId) {
          const oldParent = nodeStore.getNode(oldParentId);
          if (oldParent) {
            // Add node back to old parent's children
            oldParent.addChild(nodeId);
          }

          // Restore the node's parent reference
          node.setParent(oldParentId);
        } else {
          // Node was a root node
          node.setParent(undefined);
        }
      },
      redo: () => {
        // Update the node's parent relationship again
        if (oldParentId) {
          const oldParent = nodeStore.getNode(oldParentId);
          if (oldParent) {
            // Remove node from old parent's children
            oldParent.removeChild(nodeId);
          }
        }

        // Add node to new parent's children
        newParent.addChild(nodeId);

        // Update the node's parent reference
        node.setParent(newParentId);

        return true;
      },
      description: `Update parent relationship for "${node.title}"`
    });

    // Add command to update hierarchical IDs
    batch.addCommand({
      execute: () => {
        console.log(`[RelinkOperation] Updating hierarchical IDs for node ${nodeId} and its subtree`);

        // Calculate the new hierarchical ID based on the node's position in the new parent's children
        const childIndex = newParent.relationships.childIds.indexOf(nodeId);
        if (childIndex === -1) {
          console.error(`[RelinkOperation] Node ${nodeId} not found in new parent's children`);
          return false;
        }

        const parentHierarchicalId = newParent.hierarchicalId || '0';
        const newHierarchicalId = generateHierarchicalId(parentHierarchicalId, childIndex);

        // Use the new method to update the node and all its descendants' hierarchical IDs
        this.updateHierarchicalIdRecursively(node, newHierarchicalId);

        return true;
      },
      undo: () => {
        console.log(`[RelinkOperation] Restoring original hierarchical IDs for node ${nodeId} and its descendants`);

        // Restore the original hierarchical ID for the node
        node.hierarchicalId = originalNodeHierarchicalId;
        node.label = hierarchicalIdToLabel(originalNodeHierarchicalId);

        // Restore original hierarchical IDs for all descendants
        descendants.forEach(descendant => {
          const originalHierarchicalId = originalDescendantHierarchicalIds.get(descendant.id);
          if (originalHierarchicalId) {
            const currentDescendant = nodeStore.getNode(descendant.id);
            if (currentDescendant) {
              currentDescendant.hierarchicalId = originalHierarchicalId;
              currentDescendant.label = hierarchicalIdToLabel(originalHierarchicalId);
            }
          }
        });

        // If there was an old parent, update its children's hierarchical IDs
        if (oldParentId) {
          const oldParent = nodeStore.getNode(oldParentId);
          if (oldParent) {
            oldParent.relationships.childIds.forEach((childId, index) => {
              const child = nodeStore.getNode(childId);
              if (child) {
                const parentHierarchicalId = oldParent.hierarchicalId || '0';
                const childHierarchicalId = generateHierarchicalId(parentHierarchicalId, index);
                this.updateHierarchicalIdRecursively(child, childHierarchicalId);
              }
            });
          }
        }
      },
      redo: () => {
        console.log(`[RelinkOperation] Re-updating hierarchical IDs for node ${nodeId} and its subtree`);

        // Calculate the new hierarchical ID again
        const childIndex = newParent.relationships.childIds.indexOf(nodeId);
        if (childIndex === -1) {
          console.error(`[RelinkOperation] Node ${nodeId} not found in new parent's children during redo`);
          return false;
        }

        const parentHierarchicalId = newParent.hierarchicalId || '0';
        const newHierarchicalId = generateHierarchicalId(parentHierarchicalId, childIndex);

        // Use the new method to update the node and all its descendants' hierarchical IDs
        this.updateHierarchicalIdRecursively(node, newHierarchicalId);

        return true;
      },
      description: `Update hierarchical IDs for "${node.title}" and its subtree`
    });
  }

  /**
   * Update hierarchical ID for just the node, move children to old parent
   * @param nodeId The node ID
   * @param newParentId The new parent node ID
   * @param oldParentId The old parent node ID
   * @param batch The batch command
   */
  private updateHierarchicalIdForNodeOnly(nodeId: string, newParentId: string, oldParentId: string | undefined, batch: BatchCommand): void {
    const node = nodeStore.getNode(nodeId);
    const newParent = nodeStore.getNode(newParentId);

    if (!node || !newParent) return;

    // Store the original hierarchical ID for undo
    const originalNodeHierarchicalId = node.hierarchicalId || '';

    // Store the child IDs for relinking
    const childIds = [...node.relationships.childIds];

    // Store original child-parent relationships for undo
    const originalChildRelationships = childIds.map(childId => {
      const child = nodeStore.getNode(childId);
      return {
        childId,
        parentId: child?.relationships.parentId,
        hierarchicalId: child?.hierarchicalId || ''
      };
    });

    // Add command to update the node's parent relationship and relink children
    batch.addCommand({
      execute: () => {
        console.log(`[RelinkOperation] Updating parent relationship for node ${nodeId} and relinking ${childIds.length} children to old parent ${oldParentId}`);

        // Update the node's parent relationship
        if (oldParentId) {
          const oldParent = nodeStore.getNode(oldParentId);
          if (oldParent) {
            // Remove node from old parent's children
            oldParent.removeChild(nodeId);

            // Relink children to old parent
            childIds.forEach(childId => {
              const child = nodeStore.getNode(childId);
              if (child) {
                // Remove child from node's children
                node.removeChild(childId);

                // Update child's parent
                child.setParent(oldParentId);

                // Add child to old parent's children
                oldParent.addChild(childId);
              }
            });
          }
        }

        // Add node to new parent's children
        newParent.addChild(nodeId);

        // Update the node's parent reference
        node.setParent(newParentId);

        // Clear the node's children
        node.relationships.childIds = [];

        return true;
      },
      undo: () => {
        console.log(`[RelinkOperation] Restoring original parent relationship for node ${nodeId} and its children`);

        // Restore the node's parent relationship
        newParent.removeChild(nodeId);

        if (oldParentId) {
          const oldParent = nodeStore.getNode(oldParentId);
          if (oldParent) {
            // Add node back to old parent's children
            oldParent.addChild(nodeId);

            // Restore original child relationships
            originalChildRelationships.forEach(rel => {
              const child = nodeStore.getNode(rel.childId);
              if (child) {
                // Remove from old parent
                oldParent.removeChild(rel.childId);

                // Restore original parent
                child.setParent(rel.parentId);

                // If original parent was the node being moved, add back to its children
                if (rel.parentId === nodeId) {
                  node.addChild(rel.childId);
                }
              }
            });
          }

          // Restore the node's parent reference
          node.setParent(oldParentId);
        } else {
          // Node was a root node
          node.setParent(undefined);

          // Restore original child relationships
          originalChildRelationships.forEach(rel => {
            const child = nodeStore.getNode(rel.childId);
            if (child) {
              // Restore original parent
              child.setParent(rel.parentId);

              // If original parent was the node being moved, add back to its children
              if (rel.parentId === nodeId) {
                node.addChild(rel.childId);
              }
            }
          });
        }
      },
      redo: () => {
        console.log(`[RelinkOperation] Re-updating parent relationship for node ${nodeId} and relinking children to old parent ${oldParentId}`);

        // Update the node's parent relationship again
        if (oldParentId) {
          const oldParent = nodeStore.getNode(oldParentId);
          if (oldParent) {
            // Remove node from old parent's children
            oldParent.removeChild(nodeId);

            // Relink children to old parent again
            childIds.forEach(childId => {
              const child = nodeStore.getNode(childId);
              if (child) {
                // Remove child from node's children
                node.removeChild(childId);

                // Update child's parent
                child.setParent(oldParentId);

                // Add child to old parent's children
                oldParent.addChild(childId);
              }
            });
          }
        }

        // Add node to new parent's children
        newParent.addChild(nodeId);

        // Update the node's parent reference
        node.setParent(newParentId);

        // Clear the node's children
        node.relationships.childIds = [];

        return true;
      },
      description: `Update parent relationship for "${node.title}" and relink children`
    });

    // Add command to update hierarchical IDs
    batch.addCommand({
      execute: () => {
        console.log(`[RelinkOperation] Updating hierarchical ID for node ${nodeId}`);

        // Calculate the new hierarchical ID based on the node's position in the new parent's children
        const childIndex = newParent.relationships.childIds.indexOf(nodeId);
        if (childIndex === -1) {
          console.error(`[RelinkOperation] Node ${nodeId} not found in new parent's children`);
          return false;
        }

        const parentHierarchicalId = newParent.hierarchicalId || '0';
        const newHierarchicalId = generateHierarchicalId(parentHierarchicalId, childIndex);

        // Update just this node's hierarchical ID
        node.hierarchicalId = newHierarchicalId;
        node.label = hierarchicalIdToLabel(newHierarchicalId);

        // If there was an old parent, update hierarchical IDs for its new children
        if (oldParentId) {
          const oldParent = nodeStore.getNode(oldParentId);
          if (oldParent) {
            oldParent.relationships.childIds.forEach((childId, index) => {
              const child = nodeStore.getNode(childId);
              if (child) {
                const parentHierarchicalId = oldParent.hierarchicalId || '0';
                const childHierarchicalId = generateHierarchicalId(parentHierarchicalId, index);

                // Update this child's hierarchical ID
                child.hierarchicalId = childHierarchicalId;
                child.label = hierarchicalIdToLabel(childHierarchicalId);

                // Recursively update this child's descendants
                this.updateHierarchicalIdRecursively(child, childHierarchicalId);
              }
            });
          }
        }

        return true;
      },
      undo: () => {
        console.log(`[RelinkOperation] Restoring original hierarchical ID for node ${nodeId}`);

        // Restore the original hierarchical ID for the node
        node.hierarchicalId = originalNodeHierarchicalId;
        node.label = hierarchicalIdToLabel(originalNodeHierarchicalId);

        // Restore original hierarchical IDs for all children that were moved
        originalChildRelationships.forEach(rel => {
          const child = nodeStore.getNode(rel.childId);
          if (child) {
            child.hierarchicalId = rel.hierarchicalId;
            child.label = hierarchicalIdToLabel(rel.hierarchicalId);
          }
        });

        // If there was an old parent, update its children's hierarchical IDs
        if (oldParentId) {
          const oldParent = nodeStore.getNode(oldParentId);
          if (oldParent) {
            oldParent.relationships.childIds.forEach((childId, index) => {
              const child = nodeStore.getNode(childId);
              if (child) {
                // Skip children that were originally from the moved node
                const wasOriginalChild = originalChildRelationships.some(rel => rel.childId === childId);
                if (!wasOriginalChild) {
                  const parentHierarchicalId = oldParent.hierarchicalId || '0';
                  const childHierarchicalId = generateHierarchicalId(parentHierarchicalId, index);
                  this.updateHierarchicalIdRecursively(child, childHierarchicalId);
                }
              }
            });
          }
        }
      },
      redo: () => {
        console.log(`[RelinkOperation] Re-updating hierarchical ID for node ${nodeId}`);

        // Calculate the new hierarchical ID again
        const childIndex = newParent.relationships.childIds.indexOf(nodeId);
        if (childIndex === -1) {
          console.error(`[RelinkOperation] Node ${nodeId} not found in new parent's children during redo`);
          return false;
        }

        const parentHierarchicalId = newParent.hierarchicalId || '0';
        const newHierarchicalId = generateHierarchicalId(parentHierarchicalId, childIndex);

        // Update just this node's hierarchical ID
        node.hierarchicalId = newHierarchicalId;
        node.label = hierarchicalIdToLabel(newHierarchicalId);

        // If there was an old parent, update hierarchical IDs for its new children
        if (oldParentId) {
          const oldParent = nodeStore.getNode(oldParentId);
          if (oldParent) {
            oldParent.relationships.childIds.forEach((childId, index) => {
              const child = nodeStore.getNode(childId);
              if (child) {
                const parentHierarchicalId = oldParent.hierarchicalId || '0';
                const childHierarchicalId = generateHierarchicalId(parentHierarchicalId, index);

                // Update this child's hierarchical ID
                child.hierarchicalId = childHierarchicalId;
                child.label = hierarchicalIdToLabel(childHierarchicalId);

                // Recursively update this child's descendants
                this.updateHierarchicalIdRecursively(child, childHierarchicalId);
              }
            });
          }
        }

        return true;
      },
      description: `Update hierarchical ID for "${node.title}"`
    });
  }

  /**
   * Update parent-child relationships based on hierarchical IDs
   * This ensures that the relationships in the graph match the hierarchical structure
   */
  private updateRelationshipsFromHierarchicalIds(): void {
    console.log(`[RelinkOperation] Updating relationships from hierarchical IDs`);

    // Get all nodes
    const nodes = nodeStore.getAllNodes();

    // Clear all edges
    nodeStore.getAllEdges().forEach(edge => {
      nodeStore.removeEdge(edge.id);
    });

    // First, build a map of hierarchical IDs to nodes for faster lookups
    const hierarchicalIdMap = new Map<string, NodeModel>();
    nodes.forEach(node => {
      if (node.hierarchicalId) {
        hierarchicalIdMap.set(node.hierarchicalId, node);
      }
    });

    // Process each node
    nodes.forEach(node => {
      const hierarchicalId = node.hierarchicalId;
      if (!hierarchicalId || hierarchicalId === '0') {
        // Root node
        node.setParent(undefined);
      } else {
        // Get parent hierarchical ID
        const lastDashIndex = hierarchicalId.lastIndexOf('-');
        if (lastDashIndex === -1) {
          console.error(`[RelinkOperation] Invalid hierarchical ID: ${hierarchicalId}`);
          return;
        }

        const parentHierarchicalId = hierarchicalId.substring(0, lastDashIndex);

        // Find parent node using the map
        const parentNode = hierarchicalIdMap.get(parentHierarchicalId);
        if (parentNode) {
          // Update parent-child relationship
          node.setParent(parentNode.id);

          // Check if the child is already in the parent's children
          if (!parentNode.relationships.childIds.includes(node.id)) {
            parentNode.addChild(node.id);
          }

          // Create edge if it doesn't exist
          const edgeId = `${parentNode.id}-${node.id}`;
          if (!nodeStore.getEdge(edgeId)) {
            nodeStore.addEdge({
              id: edgeId,
              source: parentNode.id,
              target: node.id
            });
          }
        } else {
          console.error(`[RelinkOperation] Parent node with hierarchical ID ${parentHierarchicalId} not found for node ${node.id} with hierarchical ID ${hierarchicalId}`);
        }
      }
    });

    // Validate the graph structure
    this.validateGraphStructure();
  }

  /**
   * Validate the graph structure to ensure it's consistent
   * This checks for orphaned nodes, cycles, and other inconsistencies
   */
  private validateGraphStructure(): void {
    console.log(`[RelinkOperation] Validating graph structure`);

    const nodes = nodeStore.getAllNodes();

    // Check for orphaned nodes (nodes with a parent ID that doesn't exist)
    const orphanedNodes = nodes.filter(node => {
      const parentId = node.relationships.parentId;
      return parentId && !nodeStore.getNode(parentId);
    });

    if (orphanedNodes.length > 0) {
      console.error(`[RelinkOperation] Found ${orphanedNodes.length} orphaned nodes:`, orphanedNodes.map(n => n.id));

      // Fix orphaned nodes by making them root nodes
      orphanedNodes.forEach(node => {
        console.log(`[RelinkOperation] Fixing orphaned node ${node.id} by making it a root node`);
        node.setParent(undefined);
      });
    }

    // Check for nodes that are in a parent's childIds but don't have that parent as their parentId
    interface InconsistentRelationship {
      parent: string;
      child: string;
    }

    const inconsistentParentChildRelationships: InconsistentRelationship[] = [];
    nodes.forEach(node => {
      node.relationships.childIds.forEach(childId => {
        const child = nodeStore.getNode(childId);
        if (child && child.relationships.parentId !== node.id) {
          inconsistentParentChildRelationships.push({ parent: node.id, child: childId });
        }
      });
    });

    if (inconsistentParentChildRelationships.length > 0) {
      console.error(`[RelinkOperation] Found ${inconsistentParentChildRelationships.length} inconsistent parent-child relationships:`, inconsistentParentChildRelationships);

      // Fix inconsistent relationships
      inconsistentParentChildRelationships.forEach(({ parent, child }) => {
        const parentNode = nodeStore.getNode(parent);
        const childNode = nodeStore.getNode(child);

        if (parentNode && childNode) {
          console.log(`[RelinkOperation] Fixing inconsistent relationship: parent=${parent}, child=${child}`);
          childNode.setParent(parent);
        }
      });
    }

    // Check for cycles in the graph
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    const checkForCycle = (nodeId: string): boolean => {
      if (!visited.has(nodeId)) {
        visited.add(nodeId);
        recursionStack.add(nodeId);

        const node = nodeStore.getNode(nodeId);
        if (node) {
          for (const childId of node.relationships.childIds) {
            if (!visited.has(childId) && checkForCycle(childId)) {
              return true;
            } else if (recursionStack.has(childId)) {
              console.error(`[RelinkOperation] Cycle detected: ${nodeId} -> ${childId}`);
              return true;
            }
          }
        }
      }

      recursionStack.delete(nodeId);
      return false;
    };

    // Start cycle detection from root nodes
    const rootNodes = nodes.filter(node => !node.relationships.parentId);
    let cycleDetected = false;

    for (const rootNode of rootNodes) {
      if (checkForCycle(rootNode.id)) {
        cycleDetected = true;
        break;
      }
    }

    if (cycleDetected) {
      console.error(`[RelinkOperation] Cycles detected in the graph structure`);
      // Cycles are a serious issue that would require more complex fixing
      // For now, we'll just log the error
    }

    console.log(`[RelinkOperation] Graph validation complete`);
  }

  /**
   * Get all descendants of a node
   * @param nodeId The node ID
   * @returns Array of descendant nodes
   */
  private getAllDescendants(nodeId: string): NodeModel[] {
    const node = nodeStore.getNode(nodeId);
    if (!node) return [];

    const descendants: NodeModel[] = [];

    // Process each child
    node.relationships.childIds.forEach(childId => {
      const child = nodeStore.getNode(childId);
      if (child) {
        descendants.push(child);

        // Recursively get descendants of this child
        const childDescendants = this.getAllDescendants(childId);
        descendants.push(...childDescendants);
      }
    });

    return descendants;
  }

  /**
   * Rebuild hierarchical IDs for the entire graph
   * @returns True if the rebuild was successful
   */
  rebuildAllHierarchicalIds(): boolean {
    try {
      console.log('Rebuilding all hierarchical IDs...');

      // Find all root nodes
      const rootNodes = nodeStore.getAllNodes().filter(node => !node.relationships.parentId);
      console.log(`Found ${rootNodes.length} root nodes`);

      // Process each root node
      for (const rootNode of rootNodes) {
        console.log(`Processing root node: ${rootNode.id}`);

        // Update the root node's hierarchical ID
        rootNode.hierarchicalId = '0';
        rootNode.label = 'Root';

        console.log(`Updated root node ${rootNode.id} hierarchical ID to '0'`);

        // Update all children recursively
        rootNode.relationships.childIds.forEach((childId, index) => {
          const childNode = nodeStore.getNode(childId);
          if (childNode) {
            const childHierarchicalId = generateHierarchicalId('0', index);
            this.updateHierarchicalIdRecursively(childNode, childHierarchicalId);
          }
        });
      }

      // Update parent-child relationships based on hierarchical IDs
      this.updateRelationshipsFromHierarchicalIds();

      console.log('Hierarchical ID rebuild complete');
      return true;
    } catch (error) {
      console.error('Error rebuilding hierarchical IDs:', error);
      return false;
    }
  }

  /**
   * Update a node's ID and hierarchical ID, and recursively update its descendants
   * @param node The node to update
   * @param newId The new ID (same as hierarchical ID)
   * @param parentId The parent node ID
   * @deprecated This method is no longer needed with immutable node IDs. Use updateHierarchicalIdRecursively instead.
   */
  private updateNodeIdAndDescendants(node: NodeModel, newId: string, parentId: string): void {
    console.log(`DEPRECATED: updateNodeIdAndDescendants should not be used with immutable node IDs`);

    // With immutable node IDs, we should never update node IDs
    // Instead, just update the hierarchical ID
    this.updateHierarchicalIdRecursively(node, newId);
  }

  /**
   * Update a node's hierarchical ID and recursively update its descendants' hierarchical IDs
   * This method only updates hierarchical IDs without changing node IDs
   * @param node The node to update
   * @param newHierarchicalId The new hierarchical ID
   */
  private updateHierarchicalIdRecursively(node: NodeModel, newHierarchicalId: string): void {
    console.log(`[RelinkOperation] Updating hierarchical ID for node ${node.id} from ${node.hierarchicalId} to ${newHierarchicalId}`);

    // Update this node's hierarchical ID
    node.hierarchicalId = newHierarchicalId;
    node.label = hierarchicalIdToLabel(newHierarchicalId);

    // Update all children recursively
    node.relationships.childIds.forEach((childId, index) => {
      const child = nodeStore.getNode(childId);
      if (child) {
        const childHierarchicalId = generateHierarchicalId(newHierarchicalId, index);
        this.updateHierarchicalIdRecursively(child, childHierarchicalId);
      }
    });
  }

  /**
   * Update hierarchical IDs for all descendants of a node
   * @param node The parent node
   * @deprecated Use updateHierarchicalIdRecursively instead
   */
  private updateDescendantHierarchicalIds(node: NodeModel): void {
    const parentHierarchicalId = node.hierarchicalId || '0';

    // Update all children recursively
    node.relationships.childIds.forEach((childId, index) => {
      const childNode = nodeStore.getNode(childId);
      if (childNode) {
        const childHierarchicalId = generateHierarchicalId(parentHierarchicalId, index);
        this.updateHierarchicalIdRecursively(childNode, childHierarchicalId);
      }
    });
  }
  /**
   * Open the node editor for a specific node
   * @param nodeId The ID of the node to edit
   * @param options Optional settings for opening the node editor
   */
  openNodeEditor(nodeId: string, options: { skipCentering?: boolean } = {}): void {
    console.log(`[NodeController] Opening node editor for node ${nodeId}, options:`, options);
    this.activeNodeEditorId = nodeId;

    // Center on the node before opening the editor, unless explicitly skipped
    if (!options.skipCentering) {
      // Import needed controllers
      import('./TransformController').then(({ transformController }) => {
        console.log(`[NodeController] Centering on node ${nodeId} before opening editor`);

        // Center on the node with a quick animation
        transformController.centerOnNode(nodeId, {
          duration: 200, // Quick centering animation
          easing: 'easeInOut',
          adjustScale: false, // Keep the current scale
          onComplete: () => {
            console.log(`[NodeController] Node centering complete for node ${nodeId}`);
          }
        });
      }).catch(error => {
        console.error(`[NodeController] Error importing TransformController:`, error);
      });
    } else {
      console.log(`[NodeController] Skipping centering for node ${nodeId} as requested`);
    }

    // Dispatch event for components to listen to
    window.dispatchEvent(new CustomEvent('mvc-open-node-editor', {
      detail: { nodeId }
    }));
  }

  /**
   * Close the currently open node editor
   */
  closeNodeEditor(): void {
    console.log('[NodeController] Closing node editor');
    this.activeNodeEditorId = null;

    // Dispatch event for components to listen to
    window.dispatchEvent(new CustomEvent('mvc-close-node-editor'));
  }

  /**
   * Get the ID of the currently active node editor
   * @returns The active node editor ID or null if none is open
   */
  getActiveNodeEditorId(): string | null {
    return this.activeNodeEditorId;
  }
}

// Create a singleton instance
export const nodeController = new NodeController();
