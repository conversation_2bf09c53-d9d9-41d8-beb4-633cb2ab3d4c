import React from 'react';
import { Dialog } from 'primereact/dialog';
import { Button } from 'primereact/button';

interface DryRunUpgradeModalProps {
  visible: boolean;
  onHide: () => void;
  onUpgrade: () => void;
}

const DryRunUpgradeModal: React.FC<DryRunUpgradeModalProps> = ({
  visible,
  onHide,
  onUpgrade
}) => {
  return (
    <Dialog
      header="Upgrade Required"
      visible={visible}
      onHide={onHide}
      style={{ width: '500px' }}
      modal
      blockScroll={true}
      dismissableMask={false}
      closeOnEscape={false}
      closable={true}
    >
      <div style={{ padding: '1rem 0', textAlign: 'center' }}>
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          marginBottom: '2rem'
        }}>
          <div style={{
            fontSize: '4rem',
            marginBottom: '1rem'
          }}>
            🚀
          </div>

          <h3 style={{
            margin: '0 0 1rem 0',
            color: '#1f2937',
            fontSize: '1.5rem'
          }}>
            Unlock Project Management
          </h3>

          <p style={{
            margin: '0 0 1.5rem 0',
            color: '#6b7280',
            fontSize: '1.1rem',
            lineHeight: '1.6'
          }}>
            You're currently in <strong>Dry Run Mode</strong>. To save and manage projects,
            upgrade your account to get unlimited project storage.
          </p>
        </div>

        <div style={{
          backgroundColor: '#f3f4f6',
          padding: '1.5rem',
          borderRadius: '8px',
          marginBottom: '2rem'
        }}>
          <h4 style={{
            margin: '0 0 1rem 0',
            color: '#374151',
            fontSize: '1.1rem'
          }}>
            With an upgraded account you get:
          </h4>

          <ul style={{
            textAlign: 'left',
            margin: 0,
            paddingLeft: '1.5rem',
            color: '#6b7280'
          }}>
            <li style={{ marginBottom: '0.5rem' }}>✅ Unlimited project storage</li>
            <li style={{ marginBottom: '0.5rem' }}>✅ Automatic cloud backup</li>
            <li style={{ marginBottom: '0.5rem' }}>✅ Advanced collaboration features</li>
            <li style={{ marginBottom: '0.5rem' }}>✅ Priority support</li>
          </ul>
        </div>

        <div style={{
          display: 'flex',
          gap: '1rem',
          justifyContent: 'center'
        }}>
          <Button
            label="Maybe Later"
            icon="pi pi-times"
            className="p-button-text"
            onClick={onHide}
            style={{ minWidth: '120px' }}
          />
          <Button
            label="Upgrade Now"
            icon="pi pi-arrow-up"
            className="p-button-success"
            onClick={onUpgrade}
            style={{ minWidth: '120px' }}
          />
        </div>
      </div>
    </Dialog>
  );
};

export default DryRunUpgradeModal;
