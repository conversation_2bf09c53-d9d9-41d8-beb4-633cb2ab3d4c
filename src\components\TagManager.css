.tag-manager {
  background-color: #1a202c;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  color: #e2e8f0;
  width: 600px;
  max-width: 100%;
  max-height: 80vh;
  overflow: auto;
}

.tag-manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #2d3748;
}

.tag-manager-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  color: #a0aec0;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0;
  line-height: 1;
}

.close-button:hover {
  color: #e2e8f0;
}

.tag-manager-content {
  display: flex;
  padding: 20px;
}

@media (max-width: 600px) {
  .tag-manager-content {
    flex-direction: column;
  }
}

.tag-list {
  flex: 1;
  margin-right: 20px;
}

.tag-list h3 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 1.2rem;
  font-weight: 500;
}

.no-tags {
  color: #a0aec0;
  font-style: italic;
}

.tag-list ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.tag-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 4px;
  margin-bottom: 8px;
  background-color: #2d3748;
  transition: background-color 0.2s;
}

.tag-item:hover {
  background-color: #4a5568;
}

.tag-item.selected {
  background-color: #2b6cb0;
}

.tag-color {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  margin-right: 12px;
  cursor: pointer;
}

.tag-name {
  flex: 1;
  cursor: pointer;
}

.tag-actions {
  display: flex;
  gap: 8px;
}

.favorite-button,
.edit-button,
.delete-button {
  background: none;
  border: none;
  color: #a0aec0;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
}

.favorite-button {
  font-size: 1rem;
  padding: 2px 6px;
}

.favorite-button:hover {
  background-color: #4a5568;
  color: #ffd700;
}

.favorite-button.favorited {
  color: #ffd700;
}

.favorite-button.favorited:hover {
  color: #a0aec0;
}

.edit-button:hover {
  background-color: #4a5568;
  color: #e2e8f0;
}

.delete-button:hover {
  background-color: #c53030;
  color: #fff;
}

.clear-filters-button {
  margin-top: 16px;
  background-color: #4a5568;
  color: #e2e8f0;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  width: 100%;
}

.clear-filters-button:hover {
  background-color: #2d3748;
}

.tag-form {
  flex: 1;
  padding-left: 20px;
  border-left: 1px solid #2d3748;
}

@media (max-width: 600px) {
  .tag-form {
    padding-left: 0;
    padding-top: 20px;
    border-left: none;
    border-top: 1px solid #2d3748;
  }
}

.tag-form h3 {
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 1.2rem;
  font-weight: 500;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.form-group input[type="text"] {
  width: 100%;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #4a5568;
  background-color: #2d3748;
  color: #e2e8f0;
}

.color-picker {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 8px;
}

.color-option {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  cursor: pointer;
  transition: transform 0.2s;
}

.color-option:hover {
  transform: scale(1.1);
}

.color-option.selected {
  box-shadow: 0 0 0 2px #e2e8f0;
}

.custom-color-picker {
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  padding: 0;
  cursor: pointer;
}

.form-actions {
  display: flex;
  gap: 12px;
}

.create-button,
.update-button,
.cancel-button {
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  font-weight: 500;
}

.create-button,
.update-button {
  background-color: #4299e1;
  color: #fff;
}

.create-button:hover,
.update-button:hover {
  background-color: #3182ce;
}

.create-button:disabled,
.update-button:disabled {
  background-color: #4a5568;
  cursor: not-allowed;
}

.cancel-button {
  background-color: #4a5568;
  color: #e2e8f0;
}

.cancel-button:hover {
  background-color: #2d3748;
}
