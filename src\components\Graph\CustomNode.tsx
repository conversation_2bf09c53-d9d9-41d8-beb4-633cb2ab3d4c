﻿import React, { useState, useRef, useEffect, useCallback, memo, useContext } from 'react';
import ReactDOM from 'react-dom';
import type { CustomNodeData, CanvasNode, PartialCanvasNode, NodeRelationships, CanvasEdge } from '../../types';
import './CustomNode.css';
import { useGraph } from '../../context/GraphContext';
import { ColorPicker } from 'primereact/colorpicker';
import type { ColorPickerChangeEvent } from 'primereact/colorpicker';
import ProgressBar from './ProgressBar';
import NodeContent from './NodeContent';
import { calculateGraphLayout } from '../../utils/graphLayout';
import { GraphLayout } from '../../types/layouts';
import { useCanvas } from '../../context/CanvasContext';
import { useInverseScale } from '@/hooks/useInverseScale';
import NodeTags from '../Tags/NodeTags';
import TagSelector from '../Tags/TagSelector';
import HoverTagSelector from '../Tags/HoverTagSelector';
import { useTags } from '../../context/TagContext';
import TitleNodePrompt from './TitleNodePrompt';
import NodeButtonDebugger from '../Debug/NodeButtonDebugger';
import { HighlightedNodeContext } from '../../view/components/ViewInteractionLayer';

const forceOpaque = (col: string): string => {
  if (col.startsWith("#") && col.length === 9) {
    return col.substring(0, 7);
  }
  if (col.startsWith("rgba")) {
    return col.replace(/,\s*[0-9\.]+\)$/, ")").replace("rgba", "rgb");
  }
  return col;
};

interface CustomNodeProps {
  node: CanvasNode;
  onEditClick: () => void;
  onAddChildClick: (type?: 'tasks' | 'richtext' | 'deadline' | 'table' | 'reminder' | 'image', isTable?: boolean) => void;
  onDeleteNode?: (nodeId: string, deleteWithChildren: boolean) => void;
}

interface NodeTypeItem {
  label: string;
  icon: string;
  type: string;
}

function CustomNodeComponent({ node, onEditClick, onAddChildClick, onDeleteNode }: CustomNodeProps) {
  const { updateNode, nodes, setNodes, cutOperation, setCutOperation, currentLayout, setEdges, setIsDraggingNode, relinkNode, toggleNodeCollapsed } = useGraph();
  const { tags } = useTags();
  const [isCompleted, setIsCompleted] = useState(node.completed || false);
  const { scale, offset } = useCanvas(); // Get the current zoom scale and canvas offset
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [showNodeTypeMenu, setShowNodeTypeMenu] = useState(false);
  const [showTagSelector, setShowTagSelector] = useState(false);
  const [previousCustomColor, setPreviousCustomColor] = useState(node.color);
  const menuRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const speedDialRef = useRef<HTMLDivElement>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showScissorsModal, setShowScissorsModal] = useState(false);
  const [showPasteModal, setShowPasteModal] = useState(false);
  const [showRelinkModal, setShowRelinkModal] = useState(false);
  const [targetNodeId, setTargetNodeId] = useState<string | null>(null);
  const [potentialTarget, setPotentialTarget] = useState<CanvasNode | null>(null);
  const [cursorPosition, setCursorPosition] = useState({ x: 0, y: 0 });
  const [nodeHeight, setNodeHeight] = useState<number>(node.height);
  const heightUpdateTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isUpdatingSegmentsRef = useRef<boolean>(false);

  // Add a ref to track the current target node ID to ensure it's always up to date
  const currentTargetNodeIdRef = useRef<string | null>(null);
  // Add a ref to store the current target node object
  const currentTargetNodeRef = useRef<CanvasNode | null>(null);

  // Dragging state
  const [isDragging, setIsDragging] = useState(false);
  const isDraggingRef = useRef(false); // Add ref for immediate tracking of drag state
  const dragStartRef = useRef({ mouseX: 0, mouseY: 0, nodeX: 0, nodeY: 0, currentX: 0, currentY: 0 });
  const nodeRef = useRef<HTMLDivElement>(null);

  // Add path ref for the closestNode line drawing
  const svgRef = useRef<SVGSVGElement | null>(null);
  const pathRef = useRef<SVGPathElement | null>(null);

  // Add inverse scaling hook
  const { cssVars, isInverseScaling } = useInverseScale();

  // Add a ref to store the original position at the start of dragging
  const originalPositionRef = useRef<{x: number, y: number} | null>(null);

  // Add drag detection refs
  const dragStartTimeRef = useRef<number>(0);
  const dragStartPosRef = useRef<{ x: number; y: number }>({ x: 0, y: 0 });
  const hasMovedRef = useRef<boolean>(false); // Track if the node has actually moved during drag
  const DRAG_THRESHOLD = 5; // pixels
  const CLICK_TIMEOUT = 200; // milliseconds

  // Calculate font sizes based on scale using a more direct approach
  const calculateFontSize = () => {
    // Return fixed sizes now with additional 20% increase
    return {
      titleSize: 31, // Increased from 26 (26 * 1.2)
      contentSize: 28 // Increased from 23 (23 * 1.2)
    };

    /* Commented out dynamic scaling logic for future reference
    // Base sizes for font at scale 1.0
    const baseTitleSize = 16;
    const baseContentSize = 14;

    // Direct calculation - smaller scale = larger font, larger scale = smaller font
    // We invert the scale because we want larger fonts when zoomed out (small scale)
    // and smaller fonts when zoomed in (large scale)
    // Scale ranges typically from 0.1 (zoomed out) to 5 (zoomed in)

    // Limit the scale range for calculation
    const minScale = 0.1;
    const maxScale = 2.0;
    const clampedScale = Math.max(minScale, Math.min(maxScale, scale));

    // Invert the scale (1/scale) so that:
    // - When scale is 0.1 (zoomed out) → inverted = 10 → larger font
    // - When scale is 5.0 (zoomed in) → inverted = 0.2 → smaller font
    const invertedScale = 1 / clampedScale;

    // Apply the inverted scale with some adjustment for better readability
    // Limit the range to prevent extreme font sizes
    const titleSize = Math.min(62, Math.max(12, Math.round(baseTitleSize * invertedScale)));
    const contentSize = Math.min(45, Math.max(10, Math.round(baseContentSize * invertedScale)));

    return {
      titleSize,
      contentSize
    };
    */
  };

  // Get the calculated font sizes
  const { titleSize, contentSize } = calculateFontSize();

  // Font style objects with direct pixel values
  const titleStyle = {
    fontSize: `${titleSize}px`,
    lineHeight: `${titleSize * 1.5}px`,
    fontWeight: 650, // Removed scale-based weight
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
    maxWidth: '100%',
    display: 'block',
  };

  const contentStyle = {
    fontSize: `${contentSize}px`,
    lineHeight: `${contentSize * 1.2}px`,
  };

  // Add effect to log scale changes
  useEffect(() => {
    // Removed console.log for scale changes
  }, [scale]);

  // Add escape key handler to cancel cut operation
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        // Cancel any active cut operation
        if (cutOperation) {
          setCutOperation(null);
        }
        // Close any open modals
        setShowScissorsModal(false);
        setShowPasteModal(false);
        setShowDeleteModal(false);
      }
    };

    // Add event listener
    document.addEventListener('keydown', handleEscapeKey);

    // Cleanup
    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [cutOperation, setCutOperation]);

  // Add click handler to close delete modal when clicking outside
  useEffect(() => {
    if (!showDeleteModal) return;

    const handleClickOutside = (event: MouseEvent) => {
      // Close the delete modal when clicking outside
      setShowDeleteModal(false);
    };

    // Add event listener with a slight delay to prevent immediate closing
    const timeoutId = setTimeout(() => {
      document.addEventListener('click', handleClickOutside);
    }, 100);

    // Cleanup
    return () => {
      clearTimeout(timeoutId);
      document.removeEventListener('click', handleClickOutside);
    };
  }, [showDeleteModal]);

  useEffect(() => {
    // Initialize components when node is loaded with segments
    if (node.segments) {
      const tasksSegments = node.segments.filter(seg => seg.type === 'tasks');
      const deadlineSegment = node.segments.find(seg => seg.type === 'deadline');

      // Update tasks and deadline in node data if not already set
      if (tasksSegments.length > 0 && (!node.tasks || node.tasks.length === 0)) {
        const allTasks = tasksSegments.flatMap(seg => seg.tasks || []);
        updateNode(node.id, { tasks: allTasks });
      }

      if (deadlineSegment && !node.deadline) {
        updateNode(node.id, { deadline: deadlineSegment.deadline });
      }
    }
  }, [node.id, node.segments]);

  // Get parent's color for inheritance
  const getParentColor = () => {
    if (!node.relationships) return '#cccccc'; // default color
    const relationships = node.relationships as NodeRelationships;
    const parent = nodes.find(n => n.id === relationships.parentId);
    if (!parent) return '#cccccc';
    return parent.useCustomColor ? parent.color : (parent.inheritedColor || parent.color);
  };

  // Update this node and all children that inherit colors
  const updateNodeAndChildren = (nodeId: string, parentColor: string) => {
    const currentNode = nodes.find(n => n.id === nodeId);
    if (!currentNode) return;

    // If node doesn't use custom color, update its inherited color
    if (!currentNode.useCustomColor) {
      const updatedNode: PartialCanvasNode = {
        color: currentNode.color,
        useCustomColor: false,
        inheritedColor: parentColor,
        relationships: currentNode.relationships || { childIds: [] }
      };
      updateNode(nodeId, updatedNode);
    }

    // Update children recursively
    const childIds = currentNode.relationships?.childIds ?? [];
    childIds.forEach(childId => {
      updateNodeAndChildren(childId, currentNode.useCustomColor ? currentNode.color : parentColor);
    });
  };

  const handleColorInheritanceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const useCustomColor = e.target.checked;
    const parentColor = getParentColor();

    // If switching to custom color, use previous custom color if it exists
    const newColor = useCustomColor ? (previousCustomColor || node.color || '#cccccc') : parentColor;

    // Store the current custom color when switching to inherited
    if (!useCustomColor && node.useCustomColor) {
      setPreviousCustomColor(node.color);
    }

    const updatedNode: PartialCanvasNode = {
      color: newColor,
      useCustomColor,
      inheritedColor: parentColor,
      relationships: node.relationships || { childIds: [] }
    };
    updateNode(node.id, updatedNode);

    // Update all children that inherit colors
    const childIds = node.relationships?.childIds ?? [];
    childIds.forEach(childId => {
      updateNodeAndChildren(childId, useCustomColor ? newColor : parentColor);
    });

    // Automatically open color picker when enabling custom color
    if (useCustomColor) {
      setShowColorPicker(true);
    } else {
      setShowColorPicker(false);
    }
  };

  const handleColorChange = (e: ColorPickerChangeEvent) => {
    const newColor = '#' + (e.value as string);
    setPreviousCustomColor(newColor);

    const updatedNode: PartialCanvasNode = {
      color: newColor,
      relationships: node.relationships || { childIds: [] }
    };
    updateNode(node.id, updatedNode);

    // Update children that inherit color
    const childIds = node.relationships?.childIds ?? [];
    childIds.forEach(childId => {
      updateNodeAndChildren(childId, newColor);
    });
  };

  // Get the effective color (either custom or inherited)
  const effectiveColor = node.useCustomColor ? node.color : (node.inheritedColor || getParentColor());

  // Function to create/update SVG elements for the closest node path
  const createOrUpdateSvgElement = () => {
    // Check if SVG already exists in the document
    if (!svgRef.current) {
      // Create SVG element
      const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
      svg.setAttribute('style', 'position: fixed; top: 0; left: 0; width: 100%; height: 100%; pointer-events: none; overflow: visible; z-index: 9000;'); // Lower z-index for SVG
      document.body.appendChild(svg);
      svgRef.current = svg;

      // Create path element
      const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
      path.setAttribute('fill', 'none');
      path.setAttribute('stroke', '#4338ca');
      path.setAttribute('stroke-width', '4');
      path.setAttribute('stroke-dasharray', '10,5');
      path.setAttribute('opacity', '0');
      path.setAttribute('filter', 'drop-shadow(0px 0px 5px rgba(79, 70, 229, 0.7))');
      svg.appendChild(path);
      pathRef.current = path;

      // Create marker definition
      const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');
      svg.appendChild(defs);

      const marker = document.createElementNS('http://www.w3.org/2000/svg', 'marker');
      marker.setAttribute('id', 'arrowhead-' + node.id); // Make ID unique per node
      marker.setAttribute('markerWidth', '14');
      marker.setAttribute('markerHeight', '10');
      marker.setAttribute('refX', '12');
      marker.setAttribute('refY', '5');
      marker.setAttribute('orient', 'auto');
      defs.appendChild(marker);

      const polygon = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
      polygon.setAttribute('points', '0 0, 14 5, 0 10');
      polygon.setAttribute('fill', '#4338ca'); // Match stroke color
      marker.appendChild(polygon);

      // Set marker reference
      path.setAttribute('marker-end', `url(#arrowhead-${node.id})`);
    }
  };

  // Function to update the path to the closest node
  const updateClosestNodePath = (targetNode: CanvasNode | null, customNodePosition?: {x: number, y: number}) => {
    if (!pathRef.current) return;

    if (!targetNode) {
      // Hide the path if no target node
      pathRef.current.setAttribute('d', '');
      pathRef.current.setAttribute('opacity', '0');
      return;
    }

    // Get the latest target node position from the nodes array
    const latestTargetNode = nodes.find(n => n.id === targetNode.id) || targetNode;

    // Get positions adjusted for current scale and offset
    // Use the custom node position if provided (for drag operations)
    const nodeX = customNodePosition ? customNodePosition.x : node.x;
    const nodeY = customNodePosition ? customNodePosition.y : node.y;

    const sourceX = (nodeX + node.width/2) * scale + offset.x;
    const sourceY = (nodeY + node.height/2) * scale + offset.y;
    const targetX = (latestTargetNode.x + latestTargetNode.width/2) * scale + offset.x;
    const targetY = (latestTargetNode.y + latestTargetNode.height/2) * scale + offset.y;

    // Create bezier curve path
    const dx = targetX - sourceX;
    const dy = targetY - sourceY;
    const distance = Math.sqrt(dx * dx + dy * dy);

    // Adjust the control points based on distance
    const controlPointDistance = Math.min(100, distance * 0.5);

    // Control points - create a slight curve
    const controlX1 = sourceX + (dx * 0.25);
    const controlY1 = sourceY + (dy * 0.25);
    const controlX2 = sourceX + (dx * 0.75);
    const controlY2 = sourceY + (dy * 0.75);

    // Set the path
    const pathData = `M ${sourceX} ${sourceY} C ${controlX1} ${controlY1}, ${controlX2} ${controlY2}, ${targetX} ${targetY}`;
    pathRef.current.setAttribute('d', pathData);

    // Make sure the path is visible with full opacity
    pathRef.current.setAttribute('opacity', '1');

    // Set or update the animation
    const animationExists = Array.from(pathRef.current.children).some(
      child => child.nodeName.toLowerCase() === 'animate'
    );

    if (!animationExists) {
      // Reset the dasharray and offset for animation
      pathRef.current.setAttribute('stroke-dasharray', '8,4');

      // Create and add the animation
      const animate = document.createElementNS('http://www.w3.org/2000/svg', 'animate');
      animate.setAttribute('attributeName', 'stroke-dashoffset');
      animate.setAttribute('from', '24');
      animate.setAttribute('to', '0');
      animate.setAttribute('dur', '1s');
      animate.setAttribute('repeatCount', 'indefinite');
      pathRef.current.appendChild(animate);

      // Start the animation
      animate.beginElement();
    }

    // Update the arrowhead marker reference in case it changed
    pathRef.current.setAttribute('marker-end', `url(#arrowhead-${node.id})`);

    // Add highlight to target node by adding a temporary rectangle
    // Remove any existing highlight rect
    if (svgRef.current) {
      const existingHighlight = svgRef.current.querySelector('.node-highlight');
      if (existingHighlight) {
        svgRef.current.removeChild(existingHighlight);
      }

      // Create a highlight rectangle around the target node
      const highlight = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
      highlight.setAttribute('class', 'node-highlight');
      highlight.setAttribute('x', (targetNode.x * scale + offset.x - 5).toString());
      highlight.setAttribute('y', (targetNode.y * scale + offset.y - 5).toString());
      highlight.setAttribute('width', ((targetNode.width * scale) + 10).toString());
      highlight.setAttribute('height', ((targetNode.height * scale) + 10).toString());
      highlight.setAttribute('rx', '5');
      highlight.setAttribute('ry', '5');
      highlight.setAttribute('fill', 'none');
      highlight.setAttribute('stroke', '#4f46e5');
      highlight.setAttribute('stroke-width', '2');
      highlight.setAttribute('stroke-dasharray', '4,4');
      highlight.setAttribute('filter', 'drop-shadow(0px 0px 3px rgba(79, 70, 229, 0.3))');

      // Add animation to the highlight
      const highlightAnimate = document.createElementNS('http://www.w3.org/2000/svg', 'animate');
      highlightAnimate.setAttribute('attributeName', 'stroke-dashoffset');
      highlightAnimate.setAttribute('from', '0');
      highlightAnimate.setAttribute('to', '8');
      highlightAnimate.setAttribute('dur', '0.5s');
      highlightAnimate.setAttribute('repeatCount', 'indefinite');
      highlight.appendChild(highlightAnimate);

      // Add the highlight to the SVG
      svgRef.current.appendChild(highlight);

      // Start the animation
      highlightAnimate.beginElement();
    }
  };

  // Clean up SVG when component unmounts
  useEffect(() => {
    return () => {
      // Remove SVG from document
      if (svgRef.current && document.body.contains(svgRef.current)) {
        document.body.removeChild(svgRef.current);
      }
      svgRef.current = null;
      pathRef.current = null;
    };
  }, []);

  // Handle tag selection
  const handleTagsChange = (nodeId: string, tagIds: string[]) => {
    const updatedNode: PartialCanvasNode = {
      tags: tagIds
    };
    updateNode(nodeId, updatedNode);
  };

  // Get the highlighted node context
  const { registerHighlightedNode } = useContext(HighlightedNodeContext);

  // Handle mouse enter - register this node as highlighted for hover-based hit detection
  const handleMouseEnter = useCallback(() => {
    console.log(`[EventFlow] CustomNode.handleMouseEnter: node=${node.id}, title="${node.title}", registering as highlighted node`);
    registerHighlightedNode(node.id);
  }, [node.id, node.title, registerHighlightedNode]);

  // Handle mouse leave - clear this node as highlighted
  const handleMouseLeave = useCallback(() => {
    console.log(`[EventFlow] CustomNode.handleMouseLeave: node=${node.id}, title="${node.title}", clearing highlighted node`);
    registerHighlightedNode(null);
  }, [node.id, node.title, registerHighlightedNode]);

  // Clear highlighted node when component unmounts
  useEffect(() => {
    return () => {
      console.log(`[CustomNode] Component unmounting for node ${node.id}, clearing highlighted node`);
      registerHighlightedNode(null);
    };
  }, [node.id, registerHighlightedNode]);

  // Toggle tag selector visibility
  const toggleTagSelector = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowTagSelector(!showTagSelector);
  };

  const handleGlobalMouseMove = (e: MouseEvent) => {
    // DISABLED: CustomNode drag system conflicts with ViewInteractionLayer
    // ViewInteractionLayer is now the single source of truth for node dragging
    // This prevents the dual drag system conflict that was causing lag and jumping

    // Only handle relink detection, not actual dragging
    if (!isDraggingRef.current) return;

    // Prevent dragging and relinking the root node
    if (!node.relationships?.parentId) {
      return;
    }

    e.preventDefault();
    e.stopPropagation();

    // Calculate the delta from the drag start position, accounting for scale
    const dx = (e.clientX - dragStartRef.current.mouseX) / scale;
    const dy = (e.clientY - dragStartRef.current.mouseY) / scale;

    // Calculate current position for relink detection only
    const currentX = dragStartRef.current.nodeX + dx;
    const currentY = dragStartRef.current.nodeY + dy;

    // Check if the node has moved significantly
    const dragDistance = Math.sqrt(dx * dx + dy * dy);
    if (dragDistance > DRAG_THRESHOLD / 2) {
      hasMovedRef.current = true;
    }

    // REMOVED: Direct DOM manipulation that was conflicting with ViewInteractionLayer
    // ViewInteractionLayer now handles all position updates

    // Store the current position to update the state when dragging ends
    dragStartRef.current.currentX = currentX;
    dragStartRef.current.currentY = currentY;

    // Check for potential relink targets
    let closestNode: CanvasNode | null = null;
    let closestDistance = Infinity;
    // Calculate dynamic relink distance based on node width (0.5x the node width)
    // This requires more deliberate overlapping to trigger relinking
    const MAX_RELINK_DISTANCE = node.width * 0.5; // Reduced distance to require more deliberate overlapping

    // Check all nodes for potential targets
    for (const n of nodes) {
      // Skip the node being dragged
      if (n.id === node.id) continue;

      // Skip nodes in this node's subtree to prevent circular references
      // This function checks if targetId is in the subtree of nodeId
      const isInSubtree = (nodeId: string, targetId: string): boolean => {
        if (nodeId === targetId) return true;
        const foundNode = nodes.find(n => n.id === nodeId);
        if (!foundNode || !foundNode.relationships) return false;
        const childIds = foundNode.relationships?.childIds || [];
        for (const childId of childIds) {
          if (isInSubtree(childId, targetId)) return true;
        }
        return false;
      };

      // Skip if the potential target node is in the dragged node's subtree
      // OR if the dragged node is in the potential target's subtree
      // Both checks are needed to prevent circular references
      if (isInSubtree(node.id, n.id) || isInSubtree(n.id, node.id)) continue;

      // Calculate centers of both nodes
      const draggedNodeCenterX = currentX + node.width / 2;
      const draggedNodeCenterY = currentY + node.height / 2;
      const targetNodeCenterX = n.x + n.width / 2;
      const targetNodeCenterY = n.y + n.height / 2;

      // Calculate distance between centers
      const distance = Math.sqrt(
        Math.pow(draggedNodeCenterX - targetNodeCenterX, 2) +
        Math.pow(draggedNodeCenterY - targetNodeCenterY, 2)
      );

      // Calculate a dynamic max distance based on both nodes' sizes
      // This makes larger nodes slightly easier to link to, but still requires deliberate overlapping
      const targetNodeSize = Math.max(n.width, n.height);
      const dynamicMaxDistance = MAX_RELINK_DISTANCE + (targetNodeSize * 0.2); // Reduced additional distance

      // Update closest node if this is the closest so far and within dynamic max distance
      if (distance < closestDistance && distance < dynamicMaxDistance) {
        closestNode = n;
        closestDistance = distance;
      }
    }

    // If we found a close enough node, show relink UI
    if (closestNode) {
      setPotentialTarget(closestNode);
      setTargetNodeId(closestNode.id);
      currentTargetNodeIdRef.current = closestNode.id;
      currentTargetNodeRef.current = closestNode;

      // Update the path to the closest node
      updateClosestNodePath(closestNode, { x: currentX, y: currentY });

      if (nodeRef.current) {
        nodeRef.current.style.cursor = 'alias';
      }
    } else {
      // Clear relink state if no close node found
      clearRelinkState();
    }
  };

  const handleGlobalMouseUp = (e: MouseEvent) => {
    // DISABLED: CustomNode drag system completely disabled
    // ViewInteractionLayer is now the single source of truth for all node dragging
    // This prevents conflicts and ensures smooth 1:1 cursor following

    if (!isDraggingRef.current) return;

    e.preventDefault();
    e.stopPropagation();

    // Calculate time and distance to determine if this was a drag or click
    const dragEndTime = Date.now();
    const dragDuration = dragEndTime - dragStartTimeRef.current;
    const dragDistance = Math.sqrt(
      Math.pow(e.clientX - dragStartPosRef.current.x, 2) +
      Math.pow(e.clientY - dragStartPosRef.current.y, 2)
    );

    // If this was a short movement and quick release AND the node hasn't actually moved significantly, treat it as a click
    // We use hasMovedRef to ensure that even small movements that accumulate over time are detected as drags
    const isClick = dragDuration < CLICK_TIMEOUT && dragDistance < DRAG_THRESHOLD && !hasMovedRef.current;

    // Store cursor position for potential relink modal
    setCursorPosition({ x: e.clientX, y: e.clientY });

    // Reset drag states
    setIsDragging(false);
    isDraggingRef.current = false;
    setIsDraggingNode(false);

    // Remove the dragging-node class from the document body
    document.body.classList.remove('dragging-node');

    // Remove global event listeners
    document.removeEventListener('mousemove', handleGlobalMouseMove);
    document.removeEventListener('mouseup', handleGlobalMouseUp);

    if (nodeRef.current) {
      nodeRef.current.style.cursor = 'grab';
    }

    // Update the node position in the state when dragging ends
    // This ensures the node position is properly stored in the state
    if (hasMovedRef.current) {
      // Only update the position in the state if we're in custom layout mode
      // or if we're going to show the relink modal
      if (currentLayout === GraphLayout.CUSTOM || currentTargetNodeRef.current) {
        // Use the stored current position from dragStartRef
        updateNode(node.id, {
          x: dragStartRef.current.currentX,
          y: dragStartRef.current.currentY,
          manuallyPositioned: currentLayout === GraphLayout.CUSTOM
        });
      }
      // For non-custom layouts without a target node, the position will be reset in the else block below
    }

    // If we have a target node, show relink modal
    if (currentTargetNodeRef.current) {
      setShowRelinkModal(true);

      // We'll add global event listeners in useEffect when showRelinkModal changes
    } else {
      // If no target node, clean up SVG elements
      if (svgRef.current && document.body.contains(svgRef.current)) {
        document.body.removeChild(svgRef.current);
        svgRef.current = null;
        pathRef.current = null;
      }

      // For non-custom layout, reset position if no target
      if (currentLayout !== GraphLayout.CUSTOM && hasMovedRef.current) {
        // First, visually reset the node to its original position
        if (nodeRef.current && originalPositionRef.current) {
          nodeRef.current.style.transform = `translate3d(${originalPositionRef.current.x}px, ${originalPositionRef.current.y}px, 0)`;
        }

        // Then update the node state to reset its position
        updateNode(node.id, {
          manuallyPositioned: false
        });

        // Trigger layout refresh
        setNodes((prevNodes: CanvasNode[]) => {
          const updatedNodes = prevNodes.map(n => {
            if (n.id === node.id) {
              return {
                ...n,
                // Reset to original position to avoid visual jump
                x: originalPositionRef.current?.x || n.x,
                y: originalPositionRef.current?.y || n.y,
                manuallyPositioned: false
              };
            }
            return n;
          });

          return calculateGraphLayout(updatedNodes, currentLayout);
        });
      }
    }

    // Log drag information for debugging
    console.log(`Node ${node.id} drag ended: duration=${dragDuration}ms, distance=${dragDistance}px, hasMoved=${hasMovedRef.current}, isClick=${isClick}`);

    // Only trigger click handler if this was a click action and we're not showing a relink modal
    if (isClick && !showRelinkModal) {
      console.log(`Node ${node.id} click handler triggered`);
      handleNodeClick({ target: e.target } as unknown as React.MouseEvent);
    } else {
      console.log(`Node ${node.id} click handler NOT triggered - was detected as a drag operation`);
    }
  };

  // Helper function to clear relink state
  const clearRelinkState = () => {
    updateClosestNodePath(null);
    setPotentialTarget(null);
    setTargetNodeId(null);
    currentTargetNodeIdRef.current = null;
    currentTargetNodeRef.current = null;

    if (nodeRef.current) {
      nodeRef.current.style.cursor = 'grab';
    }
  };

  // Helper function to close relink modal and clean up
  const closeRelinkModal = () => {
    setShowRelinkModal(false);
    setTargetNodeId(null);
    currentTargetNodeIdRef.current = null;
    currentTargetNodeRef.current = null;

    // Clean up SVG elements
    if (svgRef.current && document.body.contains(svgRef.current)) {
      document.body.removeChild(svgRef.current);
      svgRef.current = null;
      pathRef.current = null;
    }

    // For default layout, trigger a layout refresh
    if (currentLayout !== GraphLayout.CUSTOM) {
      // First, visually reset the node to its original position
      if (nodeRef.current && originalPositionRef.current) {
        nodeRef.current.style.transform = `translate3d(${originalPositionRef.current.x}px, ${originalPositionRef.current.y}px, 0)`;
      }

      // Reset the node's manuallyPositioned flag to false
      updateNode(node.id, {
        manuallyPositioned: false
      });

      // Trigger layout refresh by applying calculateGraphLayout
      setNodes((prevNodes: CanvasNode[]) => {
        // Reset position for this node
        const updatedNodes = prevNodes.map(n => {
          if (n.id === node.id) {
            return {
              ...n,
              // Reset to original position to avoid visual jump
              x: originalPositionRef.current?.x || n.x,
              y: originalPositionRef.current?.y || n.y,
              manuallyPositioned: false
            };
          }
          return n;
        });

        // Apply the current layout algorithm
        return calculateGraphLayout(updatedNodes, currentLayout);
      });
    }
  };

  // Handle relinking the node or subtree
  const handleRelink = (withSubtree: boolean) => {
    // Prevent relinking the root node
    if (!node.relationships?.parentId) {
      alert("Cannot relink the root node!");
      closeRelinkModal();
      return;
    }

    // Use the ref instead of the state to ensure we're using the most recent target node ID
    const currentTargetId = currentTargetNodeIdRef.current;

    if (!currentTargetId) return;

    console.log('Relinking node to target:', currentTargetId);

    // Set up a temporary cut operation to track what we're doing
    setCutOperation({
      nodeId: node.id,
      withSubtree: withSubtree
    });

    // Get the source node (being moved)
    const sourceNode = nodes.find(n => n.id === node.id);
    if (!sourceNode || !sourceNode.relationships) {
      setCutOperation(null);
      return;
    }

    // Get the target node (new parent)
    const targetNode = nodes.find(n => n.id === currentTargetId);
    if (!targetNode) {
      setCutOperation(null);
      return;
    }

    // Get the original parent of the source node
    const originalParentId = sourceNode.relationships.parentId;

    // Check for circular references
    const isInSubtree = (nodeId: string, targetId: string): boolean => {
      if (nodeId === targetId) return true;
      const foundNode = nodes.find(n => n.id === nodeId);
      if (!foundNode || !foundNode.relationships) return false;
      const childIds = foundNode.relationships?.childIds || [];
      for (const childId of childIds) {
        if (isInSubtree(childId, targetId)) return true;
      }
      return false;
    };

    // Only check if target is in source's subtree to prevent circular references
    // We're removing the check for source in target's subtree to allow linking to ancestors including root
    if (isInSubtree(node.id, currentTargetId)) {
      alert("Cannot relink: would create a circular reference!");
      closeRelinkModal();
      setCutOperation(null);
      return;
    }

    // Update nodes based on whether we're moving with or without subtree
    setNodes((prevNodes: CanvasNode[]) => {
      const updatedNodes = prevNodes.map((n: CanvasNode) => {
        // If this is the source node being moved
        if (n.id === node.id) {
          return {
            ...n,
            relationships: {
              ...n.relationships,
              parentId: currentTargetId, // Change parent to target node
              // If not moving with subtree, clear childIds as they will be reassigned
              childIds: withSubtree ? n.relationships?.childIds || [] : []
            }
          } as CanvasNode;
        }

        // If this is the target node receiving the moved node
        if (n.id === currentTargetId) {
          // Only add the source node as a child if it's not already a child
          const existingChildIds = n.relationships?.childIds || [];
          if (!existingChildIds.includes(node.id)) {
            return {
              ...n,
              relationships: {
                ...n.relationships,
                childIds: [...existingChildIds, node.id]
              }
            } as CanvasNode;
          }
        }

        // If this is the original parent of the moved node
        if (n.id === originalParentId) {
          const updatedChildIds = (n.relationships?.childIds || [])
            .filter(id => id !== node.id);

          // If not moving with subtree, add source node's children to original parent
          if (!withSubtree) {
            updatedChildIds.push(...(sourceNode.relationships?.childIds || []));
          }

          return {
            ...n,
            relationships: {
              ...n.relationships,
              childIds: updatedChildIds
            }
          } as CanvasNode;
        }

        // If this is a child of the source node and we're not moving the subtree
        if (!withSubtree && sourceNode.relationships?.childIds?.includes(n.id)) {
          return {
            ...n,
            relationships: {
              ...n.relationships,
              parentId: originalParentId // Reparent to the original parent
            }
          } as CanvasNode;
        }

        return n;
      });

      // Update edges based on updated relationships
      const updatedEdges: CanvasEdge[] = [];
      updatedNodes.forEach(n => {
        if (n.relationships?.childIds) {
          n.relationships.childIds.forEach(childId => {
            const edgeId = `${n.id}-${childId}`;
            updatedEdges.push({
              id: edgeId,
              source: n.id,
              target: childId
            });
          });
        }
      });

      // Update edges separately
      setTimeout(() => {
        setEdges(updatedEdges);
      }, 0);

      // Apply the current layout
      const newNodes = currentLayout !== GraphLayout.CUSTOM
        ? calculateGraphLayout(updatedNodes, currentLayout)
        : updatedNodes;

      return newNodes;
    });

    // Clear the cut operation
    setCutOperation(null);

    // Use the closeRelinkModal function to clean up and close the modal
    closeRelinkModal();
  };

  const handleAddButtonMouseEnter = (e: React.MouseEvent) => {
    // Update cursor position when hovering over the add button
    setCursorPosition({ x: e.clientX, y: e.clientY });
    console.log('Add button mouse enter - showing node type menu');
    setShowNodeTypeMenu(true);
  };

  const handleAddButtonMouseLeave = (e: React.MouseEvent) => {
    // Don't hide immediately - let the menu handle its own mouse events
    console.log('Add button mouse leave');
  };

  const handleMenuMouseEnter = () => {
    console.log('Menu mouse enter - keeping menu visible');
    setShowNodeTypeMenu(true);
  };

  const handleMenuMouseLeave = () => {
    console.log('Menu mouse leave - hiding menu');
    setShowNodeTypeMenu(false);
  };

  const handleSpeedDialMouseLeave = (e: React.MouseEvent) => {
    const speedDialRect = speedDialRef.current?.getBoundingClientRect();
    const menuRect = menuRef.current?.getBoundingClientRect();

    if (!speedDialRect || !menuRect) {
      setShowNodeTypeMenu(false);
      return;
    }

    const mouseX = e.clientX;
    const mouseY = e.clientY;

    // Check if mouse is within the menu or the button area
    const isInMenu = mouseX >= menuRect.left &&
                    mouseX <= menuRect.right &&
                    mouseY >= menuRect.top &&
                    mouseY <= menuRect.bottom;

    const isInSpeedDial = mouseX >= speedDialRect.left &&
                         mouseX <= speedDialRect.right &&
                         mouseY >= speedDialRect.top &&
                         mouseY <= speedDialRect.bottom;

    if (!isInMenu && !isInSpeedDial) {
      setShowNodeTypeMenu(false);
    }
  };

  const handleAddButtonClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault(); // Add this to be extra safe
    // Just create a default empty node with a rich text component
    onAddChildClick('richtext', false);
  };

  // State for title node prompt
  const [showTitlePrompt, setShowTitlePrompt] = useState(false);
  const [titlePromptPosition, setTitlePromptPosition] = useState({ x: 0, y: 0 });
  const lastClickPosition = useRef({ x: 0, y: 0 });

  const handleNodeTypeSelect = (type: string, e: React.MouseEvent) => {
    console.log(`Node type selected from list: ${type}`);

    // Update cursor position with the current mouse position
    const mousePosition = { x: e.clientX, y: e.clientY };
    setCursorPosition(mousePosition);

    // Store the current mouse position for the prompt
    lastClickPosition.current = mousePosition;

    if (type === 'title') {
      // Show title node prompt
      setShowTitlePrompt(true);
      // Use the current mouse position for the prompt
      console.log('Showing title prompt at:', mousePosition.x, mousePosition.y);
      setTitlePromptPosition(mousePosition);
    } else {
      // Create and open node immediately with the selected component type
      onAddChildClick(type as 'tasks' | 'richtext' | 'deadline' | 'table' | 'reminder' | 'image', true);
    }
    setShowNodeTypeMenu(false);
  };

  // Handle adding a title node to this node
  const handleAddTitleToNode = () => {
    onAddChildClick('title' as any, true);
    setShowTitlePrompt(false);
  };

  // Handle adding this node to a title node
  const handleAddNodeToTitle = () => {
    // Create a title node as a sibling of this node
    // and then relink this node to the title node
    if (node.relationships?.parentId) {
      // First create a title node with the same parent as this node
      const parentId = node.relationships.parentId;

      // Create a new title node
      const titleNodeId = `node-${Date.now()}`;
      const titleNode: CanvasNode = {
        id: titleNodeId,
        x: node.x - 50, // Position slightly to the left
        y: node.y - 100, // Position above
        width: 336,
        height: 80,
        title: 'Title Node',
        content: '',
        color: node.color,
        label: `${node.label.split('-')[0]}-Title`,
        useCustomColor: false,
        inheritedColor: node.inheritedColor,
        type: 'title',
        relationships: {
          parentId: parentId,
          childIds: [node.id]
        },
        segments: [{
          id: Date.now(),
          type: 'title',
          content: 'Title Node'
        }]
      };

      // Add the title node to the nodes array
      setNodes(prevNodes => {
        // Create a copy of the nodes array
        const updatedNodes = [...prevNodes];

        // Add the new title node
        updatedNodes.push(titleNode);

        // Update the parent node to replace this node with the title node in its childIds
        const parentIndex = updatedNodes.findIndex(n => n.id === parentId);
        if (parentIndex !== -1) {
          const parent = updatedNodes[parentIndex];
          const updatedChildIds = parent.relationships?.childIds?.filter(id => id !== node.id) || [];
          updatedChildIds.push(titleNodeId);

          updatedNodes[parentIndex] = {
            ...parent,
            relationships: {
              ...parent.relationships,
              childIds: updatedChildIds
            }
          };
        }

        // Update this node to point to the title node as its parent
        const nodeIndex = updatedNodes.findIndex(n => n.id === node.id);
        if (nodeIndex !== -1) {
          updatedNodes[nodeIndex] = {
            ...updatedNodes[nodeIndex],
            relationships: {
              parentId: titleNodeId,
              childIds: updatedNodes[nodeIndex].relationships?.childIds || []
            }
          };
        }

        return updatedNodes;
      });
    }

    setShowTitlePrompt(false);
  };

  // Add a new handler for node clicks that checks if we're in a relink operation
  const handleNodeClick = (e: React.MouseEvent) => {
    console.log(`[EventFlow] CustomNode.handleNodeClick: node=${node.id}, title="${node.title}"`);

    // Only dispatch the node-clicked event if we're not showing a relink modal
    // This prevents the event from closing our own relink modal
    if (!showRelinkModal) {
      console.log(`[EventFlow] CustomNode.handleNodeClick: node=${node.id}, dispatching node-clicked event`);
      // Dispatch a custom event that a node was clicked
      // This will be used by other nodes to close their relink modals
      const nodeClickedEvent = new CustomEvent('node-clicked', {
        detail: { nodeId: node.id }
      });
      document.dispatchEvent(nodeClickedEvent);
    } else {
      console.log(`[EventFlow] CustomNode.handleNodeClick: node=${node.id}, NOT dispatching node-clicked event (relink modal is open)`);
    }

    // Only trigger edit if we're clicking the node itself, not its buttons or menus
    // AND we're not in the middle of a relink operation
    const target = e.target as HTMLElement;
    const isButton = target.tagName === 'BUTTON';
    const isMenu = target.closest('.node-type-menu');
    const isSpeedDial = target.closest('.node-speeddial');
    const isRelinkModal = target.closest('.relink-floating-menu');
    const isCheckbox = target.tagName === 'INPUT' && target.classList.contains('color-inherit-checkbox');
    const isColorControls = target.closest('.color-controls');
    const isColorLabel = target.classList.contains('color-checkbox-label');
    const isTagSelector = target.closest('.hover-tag-selector-container');

    if (!isButton && !isMenu && !isSpeedDial && !isRelinkModal && !isCheckbox && !isColorControls && !isColorLabel && !isTagSelector && !showRelinkModal) {
      // For title nodes, show a prompt to edit the title instead of opening the editor
      if (node.type === 'title') {
        const newTitle = prompt('Edit title:', node.title);
        if (newTitle !== null && newTitle.trim() !== '') {
          updateNode(node.id, { title: newTitle.trim() });
        }
        // Prevent opening the editor for title nodes
        return;
      } else {
        // For other nodes, open the editor as usual
        onEditClick();
      }
    }
  };

  const handleDeleteClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    // Prevent deletion of root nodes
    if (!node.relationships?.parentId) {
      return; // Do nothing for root nodes
    }
    // Store the position of the click for the delete modal
    setCursorPosition({ x: e.clientX, y: e.clientY });
    setShowDeleteModal(true);
  };

  const handleDelete = (deleteWithChildren: boolean) => {
    if (onDeleteNode) {
      onDeleteNode(node.id, deleteWithChildren);
    }
    setShowDeleteModal(false);
  };

  const handleScissorsClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowScissorsModal(true);
  };

  const handleCut = (withSubtree: boolean) => {
    // Prevent cutting root nodes (nodes without a parentId)
    if (!node.relationships?.parentId) {
      alert("Cannot cut the root node!");
      setShowScissorsModal(false);
      return;
    }

    setCutOperation({
      nodeId: node.id,
      withSubtree
    });
    setShowScissorsModal(false);
  };

  const handleInsertClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowPasteModal(true);
  };

  const handlePasteConfirm = () => {
    if (!cutOperation) return;

    const sourceNode = nodes.find(n => n.id === cutOperation.nodeId);
    if (!sourceNode) return;

    // Prevent relinking of root nodes (nodes without a parentId)
    if (!sourceNode.relationships?.parentId) {
      alert("Cannot relink the root node!");
      setShowPasteModal(false);
      setCutOperation(null);
      return;
    }

    // Check if targetId is in the subtree of nodeId
    const isInSubtree = (nodeId: string, targetId: string): boolean => {
      if (nodeId === targetId) return true;
      const foundNode = nodes.find(n => n.id === nodeId);
      if (!foundNode || !foundNode.relationships) return false;
      const childIds = foundNode.relationships?.childIds || [];
      for (const childId of childIds) {
        if (isInSubtree(childId, targetId)) return true;
      }
      return false;
    };

    // Only check if target is in source's subtree to prevent circular references
    // We're removing the check for source in target's subtree to allow linking to ancestors including root
    if (isInSubtree(cutOperation.nodeId, node.id)) {
      alert("Cannot paste a node into its own subtree (would create a circular reference)!");
      setShowPasteModal(false);
      return;
    }

    // Check if target node is the direct parent of the source node
    if (node.id === sourceNode.relationships?.parentId) {
      alert("Cannot paste a node into its immediate parent (would create a duplicate link)!");
      setShowPasteModal(false);
      return;
    }

    // Get all nodes to be moved (source node and potentially its subtree)
    const nodesToMove = new Set<string>();
    if (cutOperation.withSubtree) {
      const addNodeAndChildren = (nodeId: string) => {
        nodesToMove.add(nodeId);
        const node = nodes.find(n => n.id === nodeId);
        node?.relationships?.childIds?.forEach(childId => {
          addNodeAndChildren(childId);
        });
      };
      addNodeAndChildren(cutOperation.nodeId);
    } else {
      nodesToMove.add(cutOperation.nodeId);
    }

    // Update relationships
    setNodes((prevNodes: CanvasNode[]) => {
      const updatedNodes = prevNodes.map((n: CanvasNode) => {
        // If this is the source node being moved
        if (n.id === cutOperation.nodeId) {
          return {
            ...n,
            relationships: {
              ...n.relationships,
              parentId: node.id,
              // If not moving with subtree, clear childIds as they will be reassigned
              childIds: cutOperation.withSubtree ? n.relationships?.childIds || [] : []
            }
          } as CanvasNode;
        }

        // If this is the target node receiving the moved node
        if (n.id === node.id) {
          return {
            ...n,
            relationships: {
              ...n.relationships,
              childIds: [...(n.relationships?.childIds || []), cutOperation.nodeId]
            }
          } as CanvasNode;
        }

        // If this is the original parent of the moved node
        if (n.id === sourceNode.relationships?.parentId) {
          const updatedChildIds = (n.relationships?.childIds || [])
            .filter(id => id !== cutOperation.nodeId);

          // If not moving with subtree, add source node's children to original parent
          if (!cutOperation.withSubtree) {
            updatedChildIds.push(...(sourceNode.relationships?.childIds || []));
          }

          return {
            ...n,
            relationships: {
              ...n.relationships,
              childIds: updatedChildIds
            }
          } as CanvasNode;
        }

        // If this is a child of the source node and we're not moving the subtree
        if (!cutOperation.withSubtree && sourceNode.relationships?.childIds?.includes(n.id)) {
          return {
            ...n,
            relationships: {
              ...n.relationships,
              parentId: sourceNode.relationships?.parentId
            }
          } as CanvasNode;
        }

        return n;
      });

      // Ensure all edges reflect the updated relationships
      const updatedEdges: CanvasEdge[] = [];

      // Create edges based on node relationships
      updatedNodes.forEach(n => {
        if (n.relationships?.childIds) {
          n.relationships.childIds.forEach(childId => {
            const edgeId = `${n.id}-${childId}`;
            updatedEdges.push({
              id: edgeId,
              source: n.id,
              target: childId
            });
          });
        }
      });

      // Update edges separately
      setTimeout(() => {
        setEdges(updatedEdges);
      }, 0);

      // Apply layout to the updated nodes using current layout
      return calculateGraphLayout(updatedNodes, currentLayout);
    });

    // Clear cut operation
    setCutOperation(null);
    setShowPasteModal(false);
  };

  // Handle toggling node completion status
  const handleToggleComplete = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent node selection/opening
    e.preventDefault(); // Prevent default behavior

    // Toggle completion status
    const newCompletedState = !isCompleted;
    setIsCompleted(newCompletedState);

    // Update the node in the graph
    updateNode(node.id, {
      completed: newCompletedState
    });

    // Ensure the event doesn't bubble up to parent elements
    return false;
  };

  const handleContentHeightChange = (height: number) => {
    // Clear any pending height updates
    if (heightUpdateTimeoutRef.current) {
      clearTimeout(heightUpdateTimeoutRef.current);
      heightUpdateTimeoutRef.current = null;
    }

    // Set the new node height
    setNodeHeight(height + 40); // Add header height

    // Update node dimensions in the graph
    heightUpdateTimeoutRef.current = setTimeout(() => {
      updateNode(node.id, {
        height: height + 40 // Add header height
      });

      // Trigger graph recalculation if not in custom layout
      if (currentLayout !== GraphLayout.CUSTOM) {
        setNodes(prevNodes => calculateGraphLayout(prevNodes, currentLayout));
      }
    }, 150); // Small delay to batch updates
  };

  const handleDimensionsChange = (dimensions: { width: number; height: number }) => {
    // Update node dimensions with some padding for better appearance
    const finalWidth = Math.max(dimensions.width + 32, 450); // Add padding and ensure minimum width
    updateNode(node.id, {
      width: finalWidth,
      height: dimensions.height
    });

    // Trigger graph recalculation if not in custom layout
    if (currentLayout !== GraphLayout.CUSTOM) {
      setNodes(prevNodes => calculateGraphLayout(prevNodes, currentLayout));
    }
  };

  const handleSegmentUpdate = (segmentId: number, content: string) => {
    // Don't update if already processing a segment update
    if (isUpdatingSegmentsRef.current) return;

    isUpdatingSegmentsRef.current = true;

    const updatedSegments = node.segments?.map(segment => {
      if (segment.id === segmentId) {
        return { ...segment, content };
      }
      return segment;
    });

    // Update node with new segments
    updateNode(node.id, { segments: updatedSegments });

    // Reset update flag after a short delay
    setTimeout(() => {
      isUpdatingSegmentsRef.current = false;
    }, 50);
  };

  const handleImageUpdate = (segmentId: number, imageData: any) => {
    // Don't update if already processing a segment update
    if (isUpdatingSegmentsRef.current) return;

    isUpdatingSegmentsRef.current = true;

    const updatedSegments = node.segments?.map(segment => {
      if (segment.id === segmentId) {
        return { ...segment, imageData };
      }
      return segment;
    });

    // Update node with new segments
    updateNode(node.id, { segments: updatedSegments });

    // Reset update flag after a short delay
    setTimeout(() => {
      isUpdatingSegmentsRef.current = false;
    }, 50);
  };

  const nodeTypeItems: NodeTypeItem[] = [
    {
      label: 'Title Node',
      icon: '🏷️',
      type: 'title'
    },
    {
      label: 'Rich Text',
      icon: '📝',
      type: 'richtext'
    },
    {
      label: 'Checklist',
      icon: '✓',
      type: 'tasks'
    },
    {
      label: 'Deadline',
      icon: '⏰',
      type: 'deadline'
    },
    {
      label: 'Image',
      icon: '🖼️',
      type: 'image'
    },
    {
      label: 'Reminder',
      icon: '🔔',
      type: 'reminder'
    }
  ];

  // Render the temporary relink indicator line
  const renderRelinkLine = () => {
    if (!isDragging || !potentialTarget) return null;

    console.log("Rendering relink line from", node.id, "to", potentialTarget.id);

    // Calculate source and target positions with canvas offset
    const sourceX = node.x + node.width / 2;
    const sourceY = node.y + node.height / 2;
    const targetX = potentialTarget.x + potentialTarget.width / 2;
    const targetY = potentialTarget.y + potentialTarget.height / 2;

    // Since the SVG will be in the document body, we need to adjust for canvas offset and scale
    const adjustedSourceX = sourceX * scale + offset.x;
    const adjustedSourceY = sourceY * scale + offset.y;
    const adjustedTargetX = targetX * scale + offset.x;
    const adjustedTargetY = targetY * scale + offset.y;

    // Use a bezier curve similar to tree connections
    // Calculate control points for a nicer curve
    const midX = (adjustedSourceX + adjustedTargetX) / 2;
    const midY = (adjustedSourceY + adjustedTargetY) / 2;

    // Adjust the curve depending on the direction of the relink
    const isHorizontal = Math.abs(adjustedTargetX - adjustedSourceX) > Math.abs(adjustedTargetY - adjustedSourceY);
    const dx = isHorizontal ? 0 : (adjustedTargetX - adjustedSourceX) / 3;
    const dy = isHorizontal ? (adjustedTargetY - adjustedSourceY) / 3 : 0;

    const path = `M ${adjustedSourceX} ${adjustedSourceY} C ${midX - dx} ${midY - dy}, ${midX + dx} ${midY + dy}, ${adjustedTargetX} ${adjustedTargetY}`;

    // Create a unique marker ID to avoid conflicts with other SVGs
    const markerId = `relinkArrowhead-${node.id}`;

    // Use a more distinctive color and thicker stroke for better visibility
    const lineColor = "#2196F3"; // Bright blue for more visibility
    const strokeWidth = 6;       // Thicker stroke for better visibility
    const glowEffect = "drop-shadow(0px 0px 8px rgba(33, 150, 243, 0.8))"

    return ReactDOM.createPortal(
      <svg
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          pointerEvents: 'none',
          zIndex: 999
        }}
      >
        <defs>
          <marker
            id={markerId}
            markerWidth="12"
            markerHeight="9"
            refX="9"
            refY="4.5"
            orient="auto"
          >
            <polygon
              points="0 0, 12 4.5, 0 9"
              fill={lineColor}
            />
          </marker>
        </defs>
        <path
          d={path}
          stroke={lineColor}
          strokeWidth={strokeWidth}
          strokeDasharray="7,7"
          fill="none"
          opacity={1.0}  // Full opacity for better visibility
          markerEnd={`url(#${markerId})`}
          filter={glowEffect} // Enhanced glow effect for better visibility
        />
        {/* Enhanced highlight border around the target node */}
        <rect
          x={potentialTarget.x * scale + offset.x - 12}
          y={potentialTarget.y * scale + offset.y - 12}
          width={potentialTarget.width * scale + 24}
          height={potentialTarget.height * scale + 24}
          stroke={lineColor}
          strokeWidth={5}
          fill="none"
          rx={10}
          ry={10}
          filter={glowEffect} // Enhanced glow effect for better visibility
        />
        {/* Animated highlight */}
        <rect
          x={potentialTarget.x * scale + offset.x - 12}
          y={potentialTarget.y * scale + offset.y - 12}
          width={potentialTarget.width * scale + 24}
          height={potentialTarget.height * scale + 24}
          stroke={lineColor}
          strokeWidth={5}
          fill="none"
          strokeDasharray="8,8"
          rx={10}
          ry={10}
          opacity={0.8}
          style={{ animation: 'dashoffset 1s linear infinite' }}
        >
          <animate
            attributeName="stroke-dashoffset"
            from="0"
            to="16"
            dur="0.8s"
            repeatCount="indefinite"
          />
        </rect>
        {/* Add a background for better visibility */}
        <rect
          x={midX - 50}
          y={midY - 35}
          width={100}
          height={30}
          fill="rgba(255,255,255,0.7)"
          rx={15}
          ry={15}
          filter="blur(5px)"
          style={{ transform: 'translateZ(0)' }}
        />
        {/* Add "Link to" text for clarity */}
        <text
          x={midX}
          y={midY - 15}
          textAnchor="middle"
          fill={lineColor}
          fontWeight="bold"
          fontSize={22}
          filter="drop-shadow(1px 1px 3px rgba(255,255,255,0.9))"
        >
          Link to
        </text>
      </svg>,
      document.body
    );
  };

  // Add effect to handle global events for paste modal
  useEffect(() => {
    if (showPasteModal) {
      // Create a handler for clicks outside the paste modal
      const handleOutsideClick = (e: MouseEvent) => {
        // Check if the click is on another node
        const target = e.target as HTMLElement;
        const isAnotherNode = target.closest('.canvas-node') &&
                             !target.closest(`.canvas-node[data-node-id="${node.id}"]`);

        if (isAnotherNode) {
          // Close the paste modal if clicked on another node
          setShowPasteModal(false);
        }
      };

      // REMOVED: Wheel event handler - now centralized in ViewInteractionLayer

      // Create a handler for keyboard events (Escape key)
      const handleKeyDown = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
          setShowPasteModal(false);
        }
      };

      // Add event listeners
      document.addEventListener('mousedown', handleOutsideClick);
      // REMOVED: wheel event listener - now centralized in ViewInteractionLayer
      document.addEventListener('keydown', handleKeyDown);

      // Clean up event listeners when component unmounts or modal closes
      return () => {
        document.removeEventListener('mousedown', handleOutsideClick);
        // REMOVED: wheel event listener cleanup - now centralized in ViewInteractionLayer
        document.removeEventListener('keydown', handleKeyDown);
      };
    }
  }, [showPasteModal, node.id]);

  // Add a global click handler to close relink modal when any node is clicked
  useEffect(() => {
    // This function will be called for all node clicks in the application
    const handleGlobalNodeClick = (event: Event) => {
      // Only process if relink modal is showing
      if (showRelinkModal) {
        // Get the node ID from the event detail
        const clickedNodeId = (event as CustomEvent).detail?.nodeId;

        // Only close if the clicked node is different from this node
        // This prevents the modal from closing when clicking on the same node
        if (clickedNodeId && clickedNodeId !== node.id) {
          // Close the relink modal when another node is clicked
          closeRelinkModal();
        }
      }
    };

    // Add a global event listener for the custom 'node-clicked' event
    document.addEventListener('node-clicked', handleGlobalNodeClick);

    // Clean up
    return () => {
      document.removeEventListener('node-clicked', handleGlobalNodeClick);
    };
  }, [showRelinkModal, closeRelinkModal, node.id]);

  // Add effect to handle global events for relink modal
  useEffect(() => {
    if (showRelinkModal) {
      // Create a handler for clicks outside the relink modal
      const handleOutsideClick = (e: MouseEvent) => {
        // Check if the click is outside the relink modal
        const target = e.target as HTMLElement;
        const isRelinkModal = target.closest('.relink-floating-menu');

        // Check if the click is on another node
        const isAnotherNode = target.closest('.canvas-node') &&
                             !target.closest(`.canvas-node[data-node-id="${node.id}"]`);

        if (!isRelinkModal || isAnotherNode) {
          // Close the relink modal if clicked outside or on another node
          closeRelinkModal();
        }
      };

      // REMOVED: Wheel event handler - now centralized in ViewInteractionLayer

      // Create a handler for keyboard events (Escape key)
      const handleKeyDown = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
          closeRelinkModal();
        }
      };

      // Add event listeners
      document.addEventListener('mousedown', handleOutsideClick);
      // REMOVED: wheel event listener - now centralized in ViewInteractionLayer
      document.addEventListener('keydown', handleKeyDown);

      // Clean up event listeners when component unmounts or modal closes
      return () => {
        document.removeEventListener('mousedown', handleOutsideClick);
        // REMOVED: wheel event listener cleanup - now centralized in ViewInteractionLayer
        document.removeEventListener('keydown', handleKeyDown);
      };
    }
  }, [showRelinkModal, closeRelinkModal, currentLayout, node.id]); // Re-run when showRelinkModal changes

  // Add effect to handle closing the title node prompt when clicking outside
  useEffect(() => {
    if (showTitlePrompt) {
      // Create a handler for clicks outside the prompt
      const handleOutsideClick = (e: MouseEvent) => {
        // Check if the click is outside the title node prompt
        const target = e.target as HTMLElement;
        const isTitlePrompt = target.closest('.title-node-prompt');

        if (!isTitlePrompt) {
          // Close the title prompt if clicked outside
          setShowTitlePrompt(false);
        }
      };

      // REMOVED: Wheel event handler - now centralized in ViewInteractionLayer

      // Create a handler for keyboard events (Escape key)
      const handleKeyDown = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
          setShowTitlePrompt(false);
        }
      };

      // Add event listeners
      document.addEventListener('mousedown', handleOutsideClick);
      // REMOVED: wheel event listener - now centralized in ViewInteractionLayer
      document.addEventListener('keydown', handleKeyDown);

      // Clean up event listeners when component unmounts or prompt closes
      return () => {
        document.removeEventListener('mousedown', handleOutsideClick);
        // REMOVED: wheel event listener cleanup - now centralized in ViewInteractionLayer
        document.removeEventListener('keydown', handleKeyDown);
      };
    }
  }, [showTitlePrompt]); // Re-run when showTitlePrompt changes

  // Add a cleanup function to reset existing overlapped nodes
  useEffect(() => {
    // When this component mounts, check for existing overlapping nodes in custom layout mode
    if (currentLayout === GraphLayout.CUSTOM && node.relationships?.parentId) {
      // This will run immediately to identify overlapping nodes when app starts
      // or new nodes appear in custom layout mode
      const checkForExistingOverlaps = () => {
        // Skip if this node is being dragged (to avoid conflicts)
        if (isDragging) return;

        // Only run this once after node is rendered and positioned
        for (const n of nodes) {
          // Apply same filters as in handleNodeDrag
          if (n.id === node.id) continue;
          if (!n.relationships) continue;

          // Skip nodes in this node's subtree to prevent circular references
          // This function checks if targetId is in the subtree of nodeId
          const isInSubtree = (nodeId: string, targetId: string): boolean => {
            if (nodeId === targetId) return true;
            const foundNode = nodes.find(n => n.id === nodeId);
            if (!foundNode || !foundNode.relationships) return false;
            const childIds = foundNode.relationships.childIds || [];
            for (const childId of childIds) {
              if (isInSubtree(childId, targetId)) return true;
            }
            return false;
          };

          // Skip if the potential target node is in this node's subtree
          // OR if this node is in the potential target's subtree
          // Both checks are needed to prevent circular references
          if (isInSubtree(node.id, n.id) || isInSubtree(n.id, node.id)) continue;

          // Skip the current parent node too
          if (node.relationships && n.id === node.relationships.parentId) continue;

          // Check for bounding box overlap
          const nodeLeft = node.x;
          const nodeRight = node.x + node.width;
          const nodeTop = node.y;
          const nodeBottom = node.y + node.height;

          const targetNodeLeft = n.x;
          const targetNodeRight = n.x + n.width;
          const targetNodeTop = n.y;
          const targetNodeBottom = n.y + n.height;

          // Use a slightly smaller tolerance for existing overlaps
          // to avoid triggering for nodes that are just close
          const overlapTolerance = 5;
          const hasOverlap = !(
            nodeRight < targetNodeLeft - overlapTolerance ||
            nodeLeft > targetNodeRight + overlapTolerance ||
            nodeBottom < targetNodeTop - overlapTolerance ||
            nodeTop > targetNodeBottom + overlapTolerance
          );

          // If there's a significant overlap (>50%), trigger relink automatically
          if (hasOverlap) {
            const overlapWidth = Math.min(nodeRight, targetNodeRight) - Math.max(nodeLeft, targetNodeLeft);
            const overlapHeight = Math.min(nodeBottom, targetNodeBottom) - Math.max(nodeTop, targetNodeTop);

            if (overlapWidth > 0 && overlapHeight > 0) {
              const overlapArea = overlapWidth * overlapHeight;
              const nodeArea = node.width * node.height;
              const overlapPercentage = (overlapArea / nodeArea) * 100;

              // If overlap is very significant (over 70%), move nodes apart slightly
              // to prevent automatic relink triggers
              if (overlapPercentage > 70 && currentLayout === GraphLayout.CUSTOM) {
                console.log(`Found significant overlap between stationary nodes: ${node.id} and ${n.id}: ${overlapPercentage.toFixed(1)}%`);

                // Shift one of the nodes slightly to reduce overlap while preserving custom layout
                const shiftX = (nodeRight - targetNodeLeft + 10) / 2;
                const shiftY = (nodeBottom - targetNodeTop + 10) / 2;

                // Decide which axis to shift along based on smallest required movement
                if (Math.abs(shiftX) < Math.abs(shiftY)) {
                  // Shift horizontally
                  updateNode(node.id, {
                    x: node.x - shiftX,
                    manuallyPositioned: true
                  });
                } else {
                  // Shift vertically
                  updateNode(node.id, {
                    y: node.y - shiftY,
                    manuallyPositioned: true
                  });
                }
              }
            }
          }
        }
      };

      // Run the check after component mount with a slight delay to ensure
      // nodes are properly positioned first
      const timeoutId = setTimeout(checkForExistingOverlaps, 200);
      return () => clearTimeout(timeoutId);
    }
  }, [currentLayout, node.id, node.x, node.y, node.width, node.height, node.relationships?.parentId, nodes, updateNode]);

  return (
    <>
      <div
        ref={nodeRef}
        className={`custom-node canvas-node ${node.type || ''} ${!node.relationships?.parentId ? 'root-node' : ''} ${node.collapsed ? 'collapsed-node' : ''} ${isCompleted ? 'completed-node' : ''}`}
        data-node-id={node.id}
        title={!node.relationships?.parentId ? "Root node cannot be moved or relinked" : (node.collapsed && node.relationships?.childIds?.length ? "This node has collapsed children" : "")}
        style={{
          width: '450px',
          minWidth: '450px',
          maxWidth: '450px',
          height: 'auto',
          transform: `translate3d(${node.x}px, ${node.y}px, 0)`,
          borderColor: effectiveColor,
          transition: isDragging ? 'none' : 'transform 0.15s ease-out, width 0.3s ease-out, height 0.2s ease',
          '--scale-factor': scale,
          '--font-size': `${contentSize}px`,
          fontSize: `${contentSize}px`,
          backfaceVisibility: 'hidden',
          willChange: isDragging ? 'transform' : 'auto',
          transformOrigin: '0 0',
          zIndex: isDragging ? 1000 : 1,
          position: 'absolute', // Changed from relative to absolute
          pointerEvents: 'auto',
          boxSizing: 'border-box',
          touchAction: 'none',
          padding: '8px',
          cursor: isDragging ? 'grabbing' : 'grab'
        } as React.CSSProperties}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={(e) => {
          // Only handle click for root nodes here
          // For non-root nodes, clicks are handled in handleGlobalMouseUp
          if (!node.relationships?.parentId) {
            handleNodeClick(e);
          }
        }}
      >
        {/* Completion overlay - only shown when node is completed */}
        {isCompleted && (
          <div
            className="completion-overlay"
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '100%',
              backgroundColor: 'rgba(34, 197, 94, 0.2)', // Light green transparent overlay
              zIndex: 10,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              pointerEvents: 'none', // Allow clicks to pass through to the node
              borderRadius: '8px'
            }}
          >
            <div
              style={{
                fontSize: '64px',
                color: 'rgba(34, 197, 94, 0.8)', // Darker green for the checkmark
                fontWeight: 'bold'
              }}
            >
              ✓
            </div>
          </div>
        )}

        {/* Node Tags - only for non-title nodes */}
        {node.type !== 'title' && node.tags && node.tags.length > 0 && (
          <NodeTags tagIds={node.tags} />
        )}

        {/* Hover Tag Selector - only for non-title nodes */}
        {node.type !== 'title' && (
          <HoverTagSelector
            nodeId={node.id}
            selectedTags={node.tags || []}
            onTagsChange={handleTagsChange}
          />
        )}

        {/* Tag Selector (when button is clicked) - only for non-title nodes */}
        {node.type !== 'title' && showTagSelector && (
          <TagSelector
            nodeId={node.id}
            selectedTags={node.tags || []}
            onTagsChange={handleTagsChange}
          />
        )}

        {((node.tasks && node.tasks.length > 0) || node.deadline) && (
          <ProgressBar tasks={node.tasks || []} deadline={node.deadline} />
        )}
        <div
          className="node-header"
          style={{
            backgroundColor: effectiveColor,
            borderBottomColor: effectiveColor
          }}
        >
          <div className="node-header-content" style={{ flex: '1 1 auto', minWidth: 0, overflow: 'hidden' }}>
            {/* Add collapse/expand button only if the node has children */}
            {node.relationships?.childIds && node.relationships.childIds.length > 0 && (
              <button
                className={`collapse-button ${node.collapsed ? 'collapsed' : ''}`}
                onClick={(e) => {
                  e.stopPropagation();
                  // Toggle collapsed state using the context method
                  toggleNodeCollapsed(node.id);
                }}
                title={node.collapsed ? 'Expand subtree' : 'Collapse subtree'}
              >
                {node.collapsed ? '▶' : '▼'}
              </button>
            )}
            <div className="node-label">{node.label}</div>
            <h3 className="node-title" style={titleStyle}>{node.title}</h3>
          </div>
          <div className="node-header-buttons" style={{ flex: '0 0 auto' }}>
            {/* Tag Button - only for non-title nodes */}
            {node.type !== 'title' && (
              <button
                className="tag-button"
                onClick={toggleTagSelector}
                title="Manage tags"
              >
                <i className="pi pi-tag"></i>
              </button>
            )}

            {/* Color controls moved to bottom left corner */}
          </div>
        </div>
        <div
          className="node-content"
          style={{
            backgroundColor: 'var(--node-bg)',
            color: 'var(--node-text)',
            borderBottomColor: effectiveColor,
            paddingBottom: '15px',
            ...contentStyle,
            overflowX: 'hidden',
            width: '100%',
            height: 'auto',
            minHeight: '60px',
            transition: 'background-color var(--transition-speed), color var(--transition-speed)'
          }}
        >
          <NodeContent
            segments={node.segments || []}
            maxHeight={400}
            onHeightChange={handleContentHeightChange}
            onSegmentUpdate={handleSegmentUpdate}
            onImageUpdate={handleImageUpdate}
            scale={scale}
            calculatedFontSize={contentSize}
            onDimensionsChange={handleDimensionsChange}
          />
          {cutOperation && (
            <button
              className="insert-button"
              onClick={handleInsertClick}
              disabled={cutOperation.nodeId === node.id}
            >
              📥
            </button>
          )}
          <button
            className={`delete-button ${!node.relationships?.parentId ? 'disabled' : ''}`}
            onClick={handleDeleteClick}
            disabled={!node.relationships?.parentId}
            title={!node.relationships?.parentId ? 'Root node cannot be deleted. To delete the project, use the Projects panel.' : 'Delete node'}
            style={{
              position: 'absolute',
              top: '10px',
              right: '10px',
              zIndex: 1000,
              pointerEvents: 'auto'
            }}
          >
            🗑
          </button>
        </div>
        {/* Bottom left controls container */}
        <div className="bottom-left-controls" style={{ zIndex: 1000, pointerEvents: 'auto' }}>
          {/* Completable checkbox - only for non-title nodes */}
          {node.type !== 'title' && (
            <div
              className={`completable-checkbox ${isCompleted ? 'completed' : ''}`}
              onClick={handleToggleComplete}
              onMouseDown={(e) => e.stopPropagation()} // Prevent node drag when clicking checkbox
              onMouseUp={(e) => e.stopPropagation()} // Prevent node selection when releasing mouse
              style={{
                zIndex: 1001,
                pointerEvents: 'auto',
                width: '36px',
                height: '36px',
                borderRadius: '50%',
                backgroundColor: isCompleted ? 'rgba(34, 197, 94, 0.8)' : 'white',
                border: `2px solid ${isCompleted ? 'rgba(34, 197, 94, 0.8)' : '#ccc'}`,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                boxShadow: '0 2px 8px rgba(0,0,0,0.3)'
              }}
            >
              {isCompleted && (
                <span style={{ color: 'white', fontSize: '22px', fontWeight: 'bold' }}>✓</span>
              )}
            </div>
          )}

          {/* Color controls */}
          <div className="color-controls" title="Click to customize node color">
            <input
              type="checkbox"
              checked={node.useCustomColor}
              onChange={handleColorInheritanceChange}
              onClick={(e) => {
                e.stopPropagation();
              }}
              className="color-inherit-checkbox"
              title="Enable custom color for this node"
            />
            <span className="color-checkbox-label" onClick={(e) => {
              e.stopPropagation();
              const checkbox = e.currentTarget.previousElementSibling as HTMLInputElement;
              checkbox.checked = !checkbox.checked;
              const event = new Event('change', { bubbles: true });
              checkbox.dispatchEvent(event);
            }}>Color</span>
            {node.useCustomColor && (
              <button
                className="color-picker-button"
                onClick={(e) => {
                  e.stopPropagation();
                  setShowColorPicker(!showColorPicker);
                }}
                style={{
                  backgroundColor: effectiveColor,
                  zIndex: 1001,
                  pointerEvents: 'auto',
                  width: '32px',
                  height: '32px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  border: '2px solid white',
                  borderRadius: '6px',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.3)'
                }}
              >
                <span className="color-picker-icon">🎨</span>
              </button>
            )}
          </div>
        </div>


        {/* Debug shapes for buttons - always visible */}
        <NodeButtonDebugger nodeWidth={node.width} nodeHeight={node.height} />

        <div
          className="node-speeddial"
          ref={speedDialRef}
          onMouseLeave={handleSpeedDialMouseLeave}
          style={{
            position: 'absolute',
            bottom: '-36px',
            left: '50%',
            transform: 'translateX(-50%)',
            zIndex: 1000,
            padding: '8px',
            transformOrigin: 'center bottom',
            transition: 'transform 0.3s',
            pointerEvents: 'auto',
            overflow: 'visible',
            ...(isInverseScaling ? cssVars as React.CSSProperties : {})
          }}
        >
          <button
            ref={buttonRef}
            className="add-child-button"
            style={{
              backgroundColor: '#10b981',
              // Apply a transition to make the scaling smooth
              transition: 'transform 0.2s, background-color 0.2s',
              zIndex: 1000, // Increased z-index for better visibility
              position: 'absolute',
              bottom: '-36px',
              left: '50%',
              transform: 'translateX(-50%)',
              width: '48px',
              height: '48px',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '24px',
              color: 'white',
              boxShadow: '0 2px 8px rgba(0,0,0,0.3)',
              pointerEvents: 'auto'
            }}
            onClick={handleAddButtonClick}
            onMouseEnter={handleAddButtonMouseEnter}
            onMouseLeave={handleAddButtonMouseLeave}
          >
            +
          </button>
          {showNodeTypeMenu && (
            <>
              <div
                className="node-type-menu"
                ref={menuRef}
                // Don't let menu clicks propagate to underlying elements
                onClick={(e) => e.stopPropagation()}
                onMouseEnter={handleMenuMouseEnter}
                onMouseLeave={handleMenuMouseLeave}
                style={{
                  position: 'absolute',
                  bottom: '100%',
                  left: '50%',
                  transform: isInverseScaling ? `translateX(-50%) scale(${cssVars['--inverse-scale']})` : 'translateX(-50%)',
                  backgroundColor: 'white',
                  borderRadius: '8px',
                  boxShadow: isInverseScaling
                    ? `0 ${2 * Math.min(cssVars['--inverse-scale'], 1.3)}px ${8 * Math.min(cssVars['--inverse-scale'], 1.3)}px rgba(0,0,0,0.15)`
                    : '0 2px 8px rgba(0,0,0,0.15)',
                  padding: isInverseScaling ? `${8 * Math.min(cssVars['--inverse-scale'], 1.3)}px` : '8px',
                  display: 'flex',
                  flexDirection: 'column',
                  gap: isInverseScaling ? `${8 * Math.min(cssVars['--inverse-scale'], 1.3)}px` : '8px',
                  minWidth: isInverseScaling ? `${120 * Math.min(cssVars['--inverse-scale'], 1.3)}px` : '120px',
                  zIndex: 1001,
                  paddingBottom: '16px',
                  marginBottom: '-8px',
                  pointerEvents: 'auto',
                  fontSize: isInverseScaling ? `${16 * Math.min(cssVars['--inverse-scale'], 1.3)}px` : '16px'
                }}
              >
                {nodeTypeItems.map((item) => (
                  <button
                    key={item.type}
                    className="node-type-item"
                    onClick={(e) => handleNodeTypeSelect(item.type, e)}
                    style={isInverseScaling ? {
                      padding: `${8 * Math.min(cssVars['--inverse-scale'], 1.3)}px`,
                      gap: `${8 * Math.min(cssVars['--inverse-scale'], 1.3)}px`,
                      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                    } : undefined}
                  >
                    <span
                      className="node-type-icon"
                      style={isInverseScaling ? {
                        fontSize: `${18 * Math.min(cssVars['--inverse-scale'], 1.3)}px`,
                        width: `${20 * Math.min(cssVars['--inverse-scale'], 1.3)}px`,
                        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                      } : undefined}
                    >
                      {item.icon}
                    </span>
                    <span
                      className="node-type-label"
                      style={isInverseScaling ? {
                        fontSize: `${18 * Math.min(cssVars['--inverse-scale'], 1.3)}px`,
                        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                      } : undefined}
                    >
                      {item.label}
                    </span>
                  </button>
                ))}
              </div>
            </>
          )}
        </div>
      </div>

      {/* Render relink indicator line when needed */}
      {renderRelinkLine()}

      {showColorPicker && ReactDOM.createPortal(
        <div
          className="color-picker-modal-backdrop"
          onClick={() => setShowColorPicker(false)}
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.3)',
            zIndex: 1000,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          <div
            className="color-picker-modal"
            onClick={(e) => e.stopPropagation()}
            style={{
              background: '#fff',
              padding: '10px',
              borderRadius: '8px',
              boxShadow: '0 2px 10px rgba(0,0,0,0.2)'
            }}
          >
            <ColorPicker
              value={node.color.replace('#', '')}
              onChange={handleColorChange}
              inline
              format="hex"
              appendTo="self"
            />
          </div>
        </div>,
        document.body
      )}

      {showDeleteModal && ReactDOM.createPortal(
        <div
          className="delete-floating-menu"
          style={{
            position: 'fixed',
            left: cursorPosition.x,
            top: cursorPosition.y,
            transform: 'translate(-50%, -100%)',
            marginTop: '-10px',
            zIndex: 10000,
            display: 'flex',
            flexDirection: 'column',
            gap: '5px',
            background: 'transparent',
            pointerEvents: 'auto'
          }}
        >
          <button
            className="delete-button delete-node"
            onClick={() => handleDelete(false)}
            style={{
              padding: '8px 16px',
              background: '#ef4444',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: 'bold',
              whiteSpace: 'nowrap',
              boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
              transition: 'all 0.2s ease',
              margin: '4px 0'
            }}
            onMouseEnter={(e) => e.currentTarget.style.transform = 'scale(1.05)'}
            onMouseLeave={(e) => e.currentTarget.style.transform = 'scale(1)'}
          >
            Delete Node Only
          </button>
          {(node.relationships?.childIds?.length ?? 0) > 0 && (
            <button
              className="delete-button delete-subtree"
              onClick={() => handleDelete(true)}
              style={{
                padding: '8px 16px',
                background: '#dc2626',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: 'bold',
                whiteSpace: 'nowrap',
                boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
                transition: 'all 0.2s ease',
                margin: '4px 0'
              }}
              onMouseEnter={(e) => e.currentTarget.style.transform = 'scale(1.05)'}
              onMouseLeave={(e) => e.currentTarget.style.transform = 'scale(1)'}
            >
              Delete with Children
            </button>
          )}
          <button
            className="delete-button cancel"
            onClick={() => setShowDeleteModal(false)}
            style={{
              padding: '8px 16px',
              background: '#9ca3af',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: 'bold',
              whiteSpace: 'nowrap',
              boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
              transition: 'all 0.2s ease',
              margin: '4px 0'
            }}
            onMouseEnter={(e) => e.currentTarget.style.transform = 'scale(1.05)'}
            onMouseLeave={(e) => e.currentTarget.style.transform = 'scale(1)'}
          >
            Cancel
          </button>
        </div>,
        document.body
      )}

      {showScissorsModal && ReactDOM.createPortal(
        <div className="scissors-modal" onClick={() => setShowScissorsModal(false)}>
          <div className="scissors-modal-content" onClick={(e) => e.stopPropagation()}>
            <h3>Cut Node</h3>
            <p>How would you like to cut this node?</p>
            <div className="scissors-modal-buttons">
              <button
                className="scissors-modal-button cancel"
                onClick={() => setShowScissorsModal(false)}
              >
                Cancel
              </button>
              <button
                className="scissors-modal-button cut"
                onClick={() => handleCut(false)}
              >
                <div className="cut-icon single">
                  <div className="node-icon">◆</div>
                  <div className="line"></div>
                </div>
                Cut Node Only
              </button>
              <button
                className="scissors-modal-button cut-with-subtree"
                onClick={() => handleCut(true)}
              >
                <div className="cut-icon tree">
                  <div className="node-icon top">◆</div>
                  <div className="node-icon bottom-left">◆</div>
                  <div className="node-icon bottom-right">◆</div>
                  <div className="line-tree"></div>
                </div>
                Cut with Subtree
              </button>
            </div>
          </div>
        </div>,
        document.body
      )}

      {showPasteModal && ReactDOM.createPortal(
        <div className="paste-modal" onClick={() => setShowPasteModal(false)}>
          <div className="paste-modal-content" onClick={(e) => e.stopPropagation()}>
            <h3>Insert Node Here</h3>
            <p>Do you want to insert the cut node as a child of this node?</p>
            <div className="paste-modal-buttons">
              <button
                className="paste-modal-button cancel"
                onClick={() => setShowPasteModal(false)}
              >
                Cancel
              </button>
              <button
                className="paste-modal-button paste"
                onClick={handlePasteConfirm}
              >
                Insert Here
              </button>
            </div>
          </div>
        </div>,
        document.body
      )}

      {showTitlePrompt && ReactDOM.createPortal(
        <TitleNodePrompt
          position={titlePromptPosition}
          onAddTitleToNode={handleAddTitleToNode}
          onAddNodeToTitle={handleAddNodeToTitle}
          onCancel={() => setShowTitlePrompt(false)}
        />,
        document.body
      )}

      {showRelinkModal && ReactDOM.createPortal(
        <div
          className="relink-floating-menu"
          style={{
            position: 'fixed',
            left: cursorPosition.x,
            top: cursorPosition.y,
            transform: 'translate(-50%, -100%)',
            marginTop: '-10px',
            zIndex: 10000, // Increased z-index to be above SVG elements
            display: 'flex',
            flexDirection: 'column',
            gap: '5px',
            background: 'transparent',
            pointerEvents: 'auto'
          }}
        >
          <button
            className="relink-button link-node"
            onClick={() => handleRelink(false)}
            style={{
              padding: '8px 16px',
              background: '#2196F3',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '18px',
              whiteSpace: 'nowrap',
              boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
              transition: 'all 0.2s ease'
            }}
            onMouseEnter={(e) => e.currentTarget.style.transform = 'scale(1.05)'}
            onMouseLeave={(e) => e.currentTarget.style.transform = 'scale(1)'}
          >
            Link Node
          </button>
          {(node.relationships?.childIds?.length ?? 0) > 0 && (
            <button
              className="relink-button link-subtree"
              onClick={() => handleRelink(true)}
              style={{
                padding: '8px 16px',
                background: '#4CAF50',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '14px',
                whiteSpace: 'nowrap',
                boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => e.currentTarget.style.transform = 'scale(1.05)'}
              onMouseLeave={(e) => e.currentTarget.style.transform = 'scale(1)'}
            >
              Link Subtree
            </button>
          )}
          <button
            className="relink-button cancel"
            onClick={closeRelinkModal}
            style={{
              padding: '8px 16px',
              background: '#f44336',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '14px',
              whiteSpace: 'nowrap',
              boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
              transition: 'all 0.2s ease'
            }}
            onMouseEnter={(e) => e.currentTarget.style.transform = 'scale(1.05)'}
            onMouseLeave={(e) => e.currentTarget.style.transform = 'scale(1)'}
          >
            Cancel
          </button>
        </div>,
        document.body
      )}
    </>
  );
}

// Export a memoized version of the component to prevent unnecessary re-renders
export const CustomNode = memo(CustomNodeComponent, (prevProps, nextProps) => {
  // Only re-render if the node properties that affect rendering have changed
  return (
    prevProps.node.x === nextProps.node.x &&
    prevProps.node.y === nextProps.node.y &&
    prevProps.node.width === nextProps.node.width &&
    prevProps.node.height === nextProps.node.height &&
    prevProps.node.title === nextProps.node.title &&
    prevProps.node.collapsed === nextProps.node.collapsed &&
    prevProps.node.completed === nextProps.node.completed &&
    prevProps.node.color === nextProps.node.color &&
    prevProps.node.useCustomColor === nextProps.node.useCustomColor &&
    JSON.stringify(prevProps.node.content) === JSON.stringify(nextProps.node.content) &&
    JSON.stringify(prevProps.node.segments) === JSON.stringify(nextProps.node.segments) &&
    JSON.stringify(prevProps.node.tags) === JSON.stringify(nextProps.node.tags) &&
    JSON.stringify(prevProps.node.relationships) === JSON.stringify(nextProps.node.relationships)
  );
});