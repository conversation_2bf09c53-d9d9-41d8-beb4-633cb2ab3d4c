.infinite-canvas {
  transition: none;
}

.infinite-canvas.with-transition {
  transition: transform 0.5s ease-out;
}

.infinite-canvas.with-transition * {
  pointer-events: none;
}

.infinite-canvas .with-transition {
  transition: transform 0.25s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.infinite-canvas .with-zoom-transition {
  transition: transform 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.infinite-canvas .with-center-transition {
  transition: transform 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

/* Disable transitions when dragging for immediate response */
.infinite-canvas.is-dragging .with-transition,
.infinite-canvas.is-dragging .with-zoom-transition,
.infinite-canvas.is-dragging .with-center-transition {
  transition: none !important;
} 