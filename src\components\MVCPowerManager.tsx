/**
 * MVCPowerManager - MVC version of the PowerManager component
 *
 * This component is a drop-in replacement for the PowerManager component
 * that uses the new MVC architecture. It maintains compatibility with
 * the existing code while leveraging the new architecture.
 */

import React, { useState, useRef, useEffect, useCallback } from 'react';
import './PowerManager.css';
import { useGraph } from '@/context/GraphContext';
import { useTags } from '@/context/TagContext';
import { useCanvas } from '@/context/CanvasContext';
import { useTransform } from '@/context/TransformContext';

// Declare global variable for transform
declare global {
    interface Window {
        mvcTransform?: {
            scale: number;
            offset: {
                x: number;
                y: number;
            };
        };
    }
}
import { useTestRun } from '@/context/TestRunContext';
import { useProject } from '@/context/ProjectContext';
import { useAuthWrapper } from '@/context/AuthWrapper';

import { TierLevel, getMaxProjectsAllowed } from '@/services/userProfileService';
import DryRunWarningModal from '@/components/Projects/DryRunWarningModal';
import { showNewProjectDialog } from '@/components/Projects/ProjectList';
import QuickSearchBubble from './QuickSearchBubble';
import TagFilter from './Tags/TagFilter';
import KanbanView from './Tags/KanbanView';
import QuickSearchTags from './Tags/QuickSearchTags';
import QuickSearchSectionWithTransform from './QuickSearchSectionWithTransform';
import type { CanvasNode, PartialCanvasNode, EditorSegment, CanvasEdge } from '../types';
import SimpleLayout from '@/components/SimpleLayout';
import NodeEditorModal from '@/components/NodeEditorModal';
import InfiniteCanvas from '@/components/InfiniteCanvas';
import { GraphLayout } from '@/types/layouts';
import { loadDemoTemplate } from '@/utils/demoLoader';
import './QuickSearch.css';

import { Toast } from 'primereact/toast';

import ProjectSelectionModal from './Projects/ProjectSelectionModal';
import ExportImportModal from './ExportImportModal';
import UserProfile from './Auth/UserProfile';
import ThemeToggle from './ThemeToggle';
import { useNavigate } from 'react-router-dom';
import { GraphBridge } from './GraphBridge';
import { DirectNodeRenderer } from './DirectNodeRenderer';
import MVCNodeEditorModal from './MVCNodeEditorModal';
import KeyboardShortcutHandler from './KeyboardShortcutHandler';
import ShortcutHelpModal from './ShortcutHelpModal';

// Import MVC components
import { nodeStore } from '../model/NodeStore';
import { nodeController } from '../controller/NodeController';
import { edgeController } from '../controller/EdgeController';
import { layoutController, LayoutType } from '../controller/LayoutController';
import { commandManager } from '../controller/CommandManager';
import { projectController } from '../controller/ProjectController';
// Note: TagController is deprecated, use TagContext instead
import { filterController } from '../controller/FilterController';
import { keyboardShortcutController, ShortcutContext } from '../controller/KeyboardShortcutController';
import { SupabaseStorageAdapter } from '../model/SupabaseStorageAdapter';
import { importToMVC, exportFromMVC } from '../utils/migrationUtils';
import { nodeHighlightService } from '../services/NodeHighlightService';
import { exportProjectToFile, importProjectFromFile } from '../utils/projectUtils';
import { TagManager } from './TagManager';
import { KanbanBoard } from './KanbanBoard';
import { SearchBar } from './SearchBar';
import MigrationComponent from './MigrationComponent';

interface QuickSearchResult {
    id: string;
    type: 'node' | 'segment';
    title: string;
    description: string;
    nodeId?: string;
    matchType: 'title' | 'content' | 'label' | 'component' | 'tag';
}

// These constants are only used for node creation, not for grid layout
const GRID_NODE_WIDTH = 336;
const GRID_NODE_HEIGHT = 356;

// Map GraphLayout to LayoutType
const mapLayoutType = (graphLayout: GraphLayout): LayoutType => {
    switch (graphLayout) {
        case GraphLayout.TOP_DOWN:
            return LayoutType.TOP_DOWN;
        case GraphLayout.LEFT_RIGHT:
            return LayoutType.LEFT_RIGHT;
        case GraphLayout.RADIAL:
            return LayoutType.RADIAL;
        case GraphLayout.FORCE:
            return LayoutType.FORCE;
        case GraphLayout.CUSTOM:
        default:
            return LayoutType.CUSTOM;
    }
};

const MVCPowerManager: React.FC = () => {
    // Use the existing contexts for compatibility
    const { nodes, edges, setNodes: graphSetNodes, setEdges, currentLayout, setCurrentLayout, updateNode } = useGraph();
    const { offset, scale, setTransform } = useCanvas();
    const { isTestRun, setTestRun } = useTestRun();
    const { tags, setImportedTags } = useTags();
    const { projects, loadProject, currentProject, setCurrentProject, setDemoMode, isDemoMode, loading, createNewProject } = useProject();
    const { isFree, tierLevel, isAuthenticated, isPaid, isAdmin } = useAuthWrapper();
    const navigate = useNavigate();

    // State variables
    const [showDryRunWarningModal, setShowDryRunWarningModal] = useState(false);
    const [canvasBackgroundColor, setCanvasBackgroundColor] = useState('#2d2d2d');
    const [showProjectModal, setShowProjectModal] = useState(false);
    const [showTagManager, setShowTagManager] = useState(false);
    const [showSearchBar, setShowSearchBar] = useState(false);
    const [showShortcutHelp, setShowShortcutHelp] = useState(false);
    const [selectedNodeId, setSelectedNodeId] = useState<string | null>(null);
    const [pendingComponentType, setPendingComponentType] = useState<'tasks' | 'richtext' | 'deadline' | 'table' | 'reminder' | 'image' | null>(null);
    const [quickSearchActive, setQuickSearchActive] = useState(false);
    const [searchQuery, setSearchQuery] = useState('');
    const [searchResults, setSearchResults] = useState<QuickSearchResult[]>([]);
    const [selectedResultIndex, setSelectedResultIndex] = useState(0);
    const [visibleNodeIds, setVisibleNodeIds] = useState<Set<string>>(new Set());
    const [selectedTags, setSelectedTags] = useState<string[]>([]);
    const [selectedComponentType, setSelectedComponentType] = useState<string | null>(null);
    const [isKanbanView, setIsKanbanView] = useState(false);
    // Always use MVC architecture - legacy toggle disabled
    const useMVC = true;
    const [selectedLayout, setSelectedLayout] = useState<LayoutType>(LayoutType.TOP_DOWN); // Default layout type
    const [showMigrationComponent, setShowMigrationComponent] = useState(false); // State for migration component
    // Export/Import modal state
    const [showExportImportModal, setShowExportImportModal] = useState(false);
    const [exportImportMode, setExportImportMode] = useState<'export' | 'import'>('export');

    // Refs
    const searchInputRef = useRef<HTMLInputElement>(null);
    const searchTimeoutRef = useRef<NodeJS.Timeout>();
    const lastCenteredNodeRef = useRef<string | null>(null);
    const canvasRef = useRef<HTMLDivElement>(null);
    const infiniteCanvasRef = useRef<any>(null);
    const toast = useRef<Toast>(null);

    // Initialize MVC architecture
    useEffect(() => {
        // Sync the current layout with the MVC layout controller
        layoutController.setLayout(mapLayoutType(currentLayout));

        // Initialize the storage adapter and connect it to the NodeStore
        if (useMVC && currentProject) {
            const storageAdapter = new SupabaseStorageAdapter(currentProject.id, {
                autoSave: true,
                autoSaveInterval: 2000, // 2 seconds
                storageBucket: 'project-files'
            });

            // Set the storage adapter for the NodeStore
            nodeStore.setStorageAdapter(storageAdapter);

            // Enable auto-save
            nodeStore.enableAutoSave(2000);

            console.log('Initialized SupabaseStorageAdapter for project:', currentProject.id);

            // Check if migration is needed
            import('../utils/migrateToImmutableNodeIds').then(module => {
                const needsMigration = module.needsMigration();
                console.log('Needs migration to immutable node IDs:', needsMigration);
                setShowMigrationComponent(needsMigration);
            }).catch(error => {
                console.error('Failed to check if migration is needed:', error);
            });
        }

        // Note: Tag initialization is now handled by TagContext

        // Subscribe to layout changes if EventEmitter is available
        let unsubscribeLayout: (() => void) | undefined;

        if (typeof layoutController.on === 'function') {
            unsubscribeLayout = layoutController.on('layout:changed', () => {
                const mvcLayout = layoutController.getCurrentLayout();
                // Map MVC layout type to GraphLayout
                let graphLayout: GraphLayout;
                switch (mvcLayout) {
                    case LayoutType.TOP_DOWN:
                        graphLayout = GraphLayout.TOP_DOWN;
                        break;
                    case LayoutType.LEFT_RIGHT:
                        graphLayout = GraphLayout.LEFT_RIGHT;
                        break;
                    case LayoutType.RADIAL:
                        graphLayout = GraphLayout.RADIAL;
                        break;
                    case LayoutType.FORCE:
                        graphLayout = GraphLayout.FORCE;
                        break;
                    case LayoutType.CUSTOM:
                    default:
                        graphLayout = GraphLayout.CUSTOM;
                        break;
                }
                setCurrentLayout(graphLayout);
            });
        }

        return () => {
            if (unsubscribeLayout) {
                unsubscribeLayout();
            }

            // Disable auto-save when component unmounts
            if (useMVC) {
                nodeStore.disableAutoSave();
            }
        };
    }, [currentLayout, setCurrentLayout, useMVC, currentProject]);

    // Define handleCenterView first to avoid circular dependency
    const handleCenterView = useCallback(() => {
        console.log('Center View button clicked');

        if (useMVC) {
            // Dispatch an event to trigger centering in DirectNodeRenderer
            const centerViewEvent = new CustomEvent('mvc-center-view');
            window.dispatchEvent(centerViewEvent);
        } else {
            // Use the existing implementation
            // This would call the original PowerManager's handleCenterView
            console.log('Using legacy implementation for handleCenterView');
        }
    }, [useMVC]);

    // Handle layout change
    const handleLayoutChange = useCallback((newLayout: GraphLayout, centerViewAfterLayout: boolean = true) => {
        console.log(`Changing layout to: ${newLayout}`);
        setCurrentLayout(newLayout);

        // Update the MVC layout controller with appropriate options
        const options = {
            nodeSpacingX: 100,
            nodeSpacingY: 100,
            levelSpacing: 250,
            centerX: 0,
            centerY: 0,
            respectManualPositions: false
        };

        // For layout changes, we want to apply the layout but handle centering separately
        // Pass skipViewCentering=true to prevent automatic view centering from the layout controller
        const skipViewCentering = !centerViewAfterLayout;
        layoutController.setLayout(mapLayoutType(newLayout), options, false, skipViewCentering);

        // If we want to center the view, do it explicitly here
        if (centerViewAfterLayout) {
            setTimeout(() => {
                console.log('[MVCPowerManager] Explicitly centering view after layout change');
                handleCenterView();
            }, 200);
        } else {
            console.log('[MVCPowerManager] Skipping view centering after layout change as requested');
        }
    }, [setCurrentLayout, handleCenterView]);

    // Handle add child node - always using MVC architecture
    const handleAddChild = useCallback((parentId: string, type?: 'tasks' | 'richtext' | 'deadline' | 'table' | 'reminder' | 'image' | 'title', openImmediately?: boolean) => {
        console.log(`Creating child node with type: ${type}, openImmediately: ${openImmediately}`);

        // Always provide explicit dimensions based on node type to prevent undefined dimensions
        const nodeWidth = type === 'title' ? 450 :
                           type === 'table' ? 500 :
                           type === 'image' ? 400 : 336;

        const nodeHeight = type === 'title' ? 80 :
                            type === 'table' ? 400 :
                            type === 'image' ? 300 :
                            type === 'deadline' || type === 'reminder' ? 200 : 356;

        const childId = nodeController.addChildNode(parentId, {
            type: type || 'richtext',
            title: type === 'title' ? 'Title Node' : 'New Node',
            dimensions: {
                width: nodeWidth,
                height: nodeHeight
            }
        });

        // Only set the selectedNodeId if openImmediately is true
        if (openImmediately && childId) {
            setSelectedNodeId(childId);
            if (type === 'table') {
                setPendingComponentType('table');
            }
        }

        /* Legacy implementation kept for reference
        // Use the existing implementation
        // This would call the original PowerManager's handleAddChild
        // For now, we'll just log a message
        console.log('Using legacy implementation for handleAddChild');
        */
    }, []);

    // This function has been moved up to avoid circular dependency

    // Handle create root node - always using MVC architecture
    const handleCreateRoot = useCallback(() => {
        console.log('Creating root node with MVC architecture');

        // Create a root node with explicit dimensions
        const rootId = nodeController.createNode({
            title: 'Root Node',
            content: 'This is the root node of the graph.',
            position: { x: 0, y: 0 },
            color: '#4299e1',
            hierarchicalId: '0', // Explicitly set hierarchical ID for root node
            label: 'Root',
            type: 'richtext',
            dimensions: {
                width: 336,
                height: 356
            }
        });

        console.log(`Created root node with ID: ${rootId}`);

        // Create child nodes with explicit dimensions
        const child1Id = nodeController.addChildNode(rootId, {
            title: 'Node 1',
            content: 'This is the first child node.',
            color: '#4299e1',
            type: 'richtext',
            dimensions: {
                width: 336,
                height: 356
            }
        });

        console.log(`Created child 1 with ID: ${child1Id}`);

        const child2Id = nodeController.addChildNode(rootId, {
            title: 'Node 2',
            content: 'This is the second child node.',
            color: '#4299e1',
            type: 'richtext',
            dimensions: {
                width: 336,
                height: 356
            }
        });

        console.log(`Created child 2 with ID: ${child2Id}`);

        // Log all nodes to verify hierarchical IDs
        const allNodes = nodeStore.getAllNodes();
        console.log(`Created ${allNodes.length} nodes with hierarchical IDs:`);
        allNodes.forEach(node => {
            console.log(`Node ID: ${node.id}, Hierarchical ID: ${node.hierarchicalId}, Label: ${node.label}`);
        });

        // Apply layout but don't automatically center the view
        layoutController.applyLayout(false, true);

        // Explicitly center the view after creating root nodes
        // This is one case where we definitely want to center
        setTimeout(() => {
            console.log('[MVCPowerManager] Explicitly centering view after creating root nodes');
            handleCenterView();
        }, 200);

        /* Legacy implementation kept for reference
        // Use the existing implementation
        // This would call the original PowerManager's handleCreateRoot
        console.log('Using legacy implementation for handleCreateRoot');
        */
    }, [handleCenterView]);

    // Handle node navigation
    const handleNodeNavigation = useCallback((nodeId: string) => {
        setSelectedNodeId(nodeId);

        if (useMVC) {
            // Use the MVC architecture
            const node = nodeStore.getNode(nodeId);

            if (node && canvasRef.current) {
                // Center the view on the selected node
                const centerX = node.position.x + node.dimensions.width / 2;
                const centerY = node.position.y + node.dimensions.height / 2;

                console.log('Navigating to node:', { nodeId, centerX, centerY });

                const containerWidth = window.innerWidth;
                const containerHeight = window.innerHeight - 60; // Account for toolbar

                // Convert to screen coordinates for the InfiniteCanvas transform
                const newOffset = {
                    x: containerWidth / 2 - centerX * scale,
                    y: containerHeight / 2 - centerY * scale
                };

                console.log('New navigation offset:', newOffset);

                // Apply the offset change
                setTransform({ offset: newOffset });
            }
        } else {
            // Use the existing implementation
            // This would call the original PowerManager's handleNodeNavigation
            console.log('Using legacy implementation for handleNodeNavigation');
        }
    }, [useMVC, scale, setTransform]);

    // Handle modal close
    const handleModalClose = useCallback((updatedData: PartialCanvasNode) => {
        if (selectedNodeId) {
            console.log('Closing modal with updated data:', updatedData);

            if (useMVC) {
                // Use the MVC architecture
                const node = nodeStore.getNode(selectedNodeId);
                if (!node) return;

                // Update the node with the new data
                nodeController.updateNode(selectedNodeId, {
                    title: updatedData.title,
                    content: updatedData.content,
                    segments: updatedData.segments,
                    color: updatedData.color,
                    useCustomColor: updatedData.useCustomColor,
                    inheritedColor: updatedData.inheritedColor,
                    tags: updatedData.tags
                });

                // Center the view on the node
                const containerWidth = window.innerWidth;
                const containerHeight = window.innerHeight;

                // Store current transform for reference
                const currentTransform = window.mvcTransform || { scale: 0.7, offset: { x: 0, y: 0 } };

                // Use the current scale or default to 0.7
                const scale = currentTransform.scale;

                // Calculate the new offset to center the node
                const newOffset = {
                    x: containerWidth / 2 - (node.position.x + node.dimensions.width / 2) * scale,
                    y: containerHeight / 2 - (node.position.y + node.dimensions.height / 2) * scale
                };

                console.log('Setting transform after modal close:', { scale, offset: newOffset });

                // Set the transform
                setTransform({
                    scale,
                    offset: newOffset
                });

                // Update window.mvcTransform for reference by other components
                window.mvcTransform = {
                    scale,
                    offset: newOffset
                };
            } else {
                // Use the existing implementation
                // This would call the original PowerManager's handleModalClose
                console.log('Using legacy implementation for handleModalClose');
            }
        }
        setSelectedNodeId(null);
    }, [useMVC, selectedNodeId, setTransform]);

    // Handle tag selection
    const handleTagSelect = useCallback((tagId: string) => {
        console.log('[MVCPowerManager] handleTagSelect called with tagId:', tagId);
        setSelectedTags(prev => {
            if (prev.includes(tagId)) {
                console.log('[MVCPowerManager] Tag already selected, no change');
                return prev;
            }
            const newTags = [...prev, tagId];
            console.log('[MVCPowerManager] New selected tags:', newTags);
            // Get tag data with colors for highlighting service
            const tagsData = newTags.map(id => {
                const tag = tags.find(t => t.id === id);
                return tag ? { id: tag.id, color: tag.color } : { id, color: '#4299e1' };
            });
            // Update node highlighting service
            nodeHighlightService.setSelectedTags(newTags, tagsData);
            return newTags;
        });
    }, [tags]);

    // Handle tag deselection
    const handleTagDeselect = useCallback((tagId: string) => {
        console.log('[MVCPowerManager] handleTagDeselect called with tagId:', tagId);
        setSelectedTags(prev => {
            const newTags = prev.filter(id => id !== tagId);
            console.log('[MVCPowerManager] New selected tags after deselection:', newTags);
            // Get tag data with colors for highlighting service
            const tagsData = newTags.map(id => {
                const tag = tags.find(t => t.id === id);
                return tag ? { id: tag.id, color: tag.color } : { id, color: '#4299e1' };
            });
            // Update node highlighting service
            nodeHighlightService.setSelectedTags(newTags, tagsData);
            return newTags;
        });
    }, [tags]);

    // Toggle MVC architecture function removed - always using MVC architecture

    // Handle project changes
    useEffect(() => {
        if (currentProject && useMVC) {
            console.log('Project changed, loading nodes from GraphContext');
            // Import nodes from GraphContext to MVC
            importToMVC(nodes, edges);

            // Center the view after a short delay to allow nodes to be processed
            setTimeout(() => {
                handleCenterView();
            }, 100);
        }
    }, [currentProject, useMVC, nodes, edges, handleCenterView]);

    // Listen for custom navigation events from MVCNodeEditorModal
    useEffect(() => {
        const handleNavigateToNode = (event: CustomEvent) => {
            const { nodeId } = event.detail;
            if (nodeId) {
                setSelectedNodeId(nodeId);
            }
        };

        // Listen for node editor open/close events
        const handleOpenNodeEditor = (event: CustomEvent) => {
            console.log('[MVCPowerManager] Received mvc-open-node-editor event', event.detail);
            const { nodeId } = event.detail;
            if (nodeId) {
                setSelectedNodeId(nodeId);
            }
        };

        const handleCloseNodeEditor = () => {
            console.log('[MVCPowerManager] Received mvc-close-node-editor event');
            setSelectedNodeId(null);
            setPendingComponentType(null);
        };

        window.addEventListener('mvc:navigate-to-node', handleNavigateToNode as EventListener);
        window.addEventListener('mvc-open-node-editor', handleOpenNodeEditor as EventListener);
        window.addEventListener('mvc-close-node-editor', handleCloseNodeEditor as EventListener);

        return () => {
            window.removeEventListener('mvc:navigate-to-node', handleNavigateToNode as EventListener);
            window.removeEventListener('mvc-open-node-editor', handleOpenNodeEditor as EventListener);
            window.removeEventListener('mvc-close-node-editor', handleCloseNodeEditor as EventListener);
        };
    }, []);

    // Listen for layout:refresh events from LayoutController
    useEffect(() => {
        const handleLayoutRefresh = (event?: CustomEvent) => {
            console.log('[MVCPowerManager] Received layout:refresh event, forcing UI update');

            // Force a refresh of the node positions in the view without centering
            const refreshEvent = new CustomEvent('mvc-refresh-view', {
                detail: { skipViewCentering: true }
            });
            window.dispatchEvent(refreshEvent);

            // Check if we should skip view centering
            const skipViewCentering = event?.detail?.skipViewCentering === true;

            // Only center the view if explicitly requested (not skipped) AND
            // if the event was triggered by a layout change, not a node addition
            if (!skipViewCentering && event?.detail?.source === 'layoutChange') {
                setTimeout(() => {
                    console.log('[MVCPowerManager] Centering view after layout refresh');
                    handleCenterView();
                }, 200);
            } else {
                console.log('[MVCPowerManager] Skipping view centering as requested or not needed');
            }
        };

        // Subscribe to layout:refresh events
        const unsubscribe = layoutController.on('layout:refresh', handleLayoutRefresh);

        return () => {
            // Unsubscribe when component unmounts
            if (unsubscribe) unsubscribe();
        };
    }, [handleCenterView]);

    // Listen for keyboard shortcut events
    useEffect(() => {
        // Handle search activation
        const handleActivateSearch = (event: CustomEvent) => {
            if (!selectedNodeId) {
                setQuickSearchActive(true);
                setSearchQuery(event.detail?.initialQuery || '');
                requestAnimationFrame(() => {
                    if (searchInputRef.current) {
                        searchInputRef.current.focus();
                    }
                });
            }
        };

        // Handle escape key
        const handleEscape = () => {
            // Close any open modals or active states
            if (quickSearchActive) {
                setQuickSearchActive(false);
                setSearchQuery('');
            } else if (showTagManager) {
                setShowTagManager(false);
            } else if (showProjectModal) {
                setShowProjectModal(false);
            } else if (showShortcutHelp) {
                setShowShortcutHelp(false);
            } else if (selectedNodeId) {
                setSelectedNodeId(null);
            }
        };

        // Handle node navigation
        const handleNavigate = (event: CustomEvent) => {
            const { direction } = event.detail;
            if (!direction) return;

            // Get the currently selected node or the first node if none is selected
            const currentNodeId = selectedNodeId || nodeStore.getAllNodes()[0]?.id;
            if (!currentNodeId) return;

            const currentNode = nodeStore.getNode(currentNodeId);
            if (!currentNode) return;

            let targetNodeId: string | undefined;

            switch (direction) {
                case 'up':
                    // Navigate to parent
                    targetNodeId = currentNode.relationships.parentId;
                    break;
                case 'down':
                    // Navigate to first child
                    targetNodeId = currentNode.relationships.childIds[0];
                    break;
                case 'left':
                    // Navigate to previous sibling
                    if (currentNode.relationships.parentId) {
                        const parentNode = nodeStore.getNode(currentNode.relationships.parentId);
                        if (parentNode) {
                            const siblings = parentNode.relationships.childIds;
                            const currentIndex = siblings.indexOf(currentNodeId);
                            if (currentIndex > 0) {
                                targetNodeId = siblings[currentIndex - 1];
                            }
                        }
                    }
                    break;
                case 'right':
                    // Navigate to next sibling
                    if (currentNode.relationships.parentId) {
                        const parentNode = nodeStore.getNode(currentNode.relationships.parentId);
                        if (parentNode) {
                            const siblings = parentNode.relationships.childIds;
                            const currentIndex = siblings.indexOf(currentNodeId);
                            if (currentIndex < siblings.length - 1) {
                                targetNodeId = siblings[currentIndex + 1];
                            }
                        }
                    }
                    break;
            }

            if (targetNodeId) {
                handleNodeNavigation(targetNodeId);
            }
        };

        // Handle view toggling
        const handleToggleView = (event: CustomEvent) => {
            const { view } = event.detail;
            if (view === 'kanban') {
                setIsKanbanView(prev => !prev);
            }
        };



        // Handle delete node
        const handleDeleteNode = async () => {
            if (selectedNodeId) {
                // Use the nodeController's deleteNode method which now shows a custom modal
                const success = await nodeController.deleteNode(selectedNodeId);
                if (success) {
                    setSelectedNodeId(null);
                }
            }
        };

        // Register event listeners
        window.addEventListener('mvc:activate-search', handleActivateSearch as EventListener);
        window.addEventListener('mvc:escape', handleEscape as EventListener);
        window.addEventListener('mvc:navigate', handleNavigate as EventListener);
        window.addEventListener('mvc:toggle-view', handleToggleView as EventListener);
        window.addEventListener('mvc:delete-node', handleDeleteNode as EventListener);

        // Clean up
        return () => {
            window.removeEventListener('mvc:activate-search', handleActivateSearch as EventListener);
            window.removeEventListener('mvc:escape', handleEscape as EventListener);
            window.removeEventListener('mvc:navigate', handleNavigate as EventListener);
            window.removeEventListener('mvc:toggle-view', handleToggleView as EventListener);
            window.removeEventListener('mvc:delete-node', handleDeleteNode as EventListener);
        };
    }, [selectedNodeId, quickSearchActive, showTagManager, showProjectModal, showShortcutHelp, handleNodeNavigation]);

    // Handle new project creation - opens project creation modal
    const handleNewProject = useCallback(() => {
        console.log('handleNewProject called with user tier:', { isAdmin, isPaid, isFree });

        // Different behavior based on user tier and state

        // Case 1: User is in Dry Run mode and tries to create a new project
        if (isTestRun && nodeStore.getAllNodes().length > 0) {
            console.log('Case 1: User in Dry Run mode with existing nodes');
            // Show warning modal
            setShowDryRunWarningModal(true);
            return;
        }

        // Case 2: User is Tier 0 (Admin) or Tier 1 (Paid) - Show project creation modal
        if (isAuthenticated && (isAdmin || isPaid)) {
            console.log('Case 2: Tier 0/1 user, showing project creation modal');
            setShowProjectModal(true);
            return;
        }

        // Case 3: User is Tier 2 (Free) with no projects - Show project creation modal
        if (isAuthenticated && isFree && (!projects || projects.length === 0)) {
            console.log('Case 3: Tier 2 user with no projects, showing project creation modal');
            setShowProjectModal(true);
            return;
        }

        // Case 4: User is Tier 2 (Free) with one project - Enter true Dry Run mode
        if (isAuthenticated && isFree && projects && projects.length >= getMaxProjectsAllowed(TierLevel.Free)) {
            console.log('T2 user with max projects, entering true Dry Run mode');

            // Clear existing nodes
            nodeStore.clear();

            // Unselect current project to prevent accidental overwriting
            setCurrentProject(null);

            // Enter Dry Run mode
            setTestRun(true);

            // Show toast notification
            if (toast.current) {
                toast.current.show({
                    severity: 'info',
                    summary: 'Dry Run Mode',
                    detail: 'Free accounts are limited to one project. You\'ve entered Dry Run mode where changes won\'t be saved to your account.',
                    life: 8000
                });
            }

            // After a short delay to ensure Dry Run mode is fully applied
            setTimeout(() => {
                // Create a new root node
                handleCreateRoot();
                // Center the view on the new root node
                handleCenterView();
            }, 100);

            return;
        }

        // Case 5: Unsigned user - Show project creation modal (will work in dry run mode)
        console.log('Case 5: Unsigned user, showing project creation modal');
        setShowProjectModal(true);
    }, [isAuthenticated, isAdmin, isPaid, isFree, projects, isTestRun, setCurrentProject, setTestRun, handleCreateRoot, handleCenterView]);

    // Handle load demo - automatically triggers dry run mode regardless of tier
    const handleLoadDemo = useCallback(() => {
        console.log('Loading demo - automatically entering dry run mode');

        // Clear existing nodes
        nodeStore.clear();

        // Unselect current project to prevent accidental overwriting
        setCurrentProject(null);

        // Enter Dry Run mode regardless of user tier
        setTestRun(true);

        // Set demo mode
        setDemoMode(true);

        // Show toast notification
        if (toast.current) {
            toast.current.show({
                severity: 'info',
                summary: 'Demo Mode',
                detail: 'Demo project loaded in Dry Run mode. Changes will not be saved to your account.',
                life: 5000
            });
        }

        // Load the demo template
        loadDemoTemplate().then(demoData => {
            console.log('Demo template loaded:', {
                nodeCount: demoData.nodes.length,
                edgeCount: demoData.edges.length,
                firstNode: demoData.nodes[0]
            });

            // Import the demo data to MVC
            importToMVC(demoData.nodes, demoData.edges);

            // Verify nodes were imported
            const importedNodes = nodeStore.getAllNodes();
            console.log('Nodes after import:', importedNodes.length);

            if (importedNodes.length > 0) {
                console.log('First imported node:', importedNodes[0].toJSON());
            }

            // Explicitly rebuild hierarchical IDs to ensure all nodes have proper IDs
            console.log('Rebuilding hierarchical IDs for demo nodes...');
            nodeStore.rebuildHierarchicalIds();

            // Log all nodes after rebuilding hierarchical IDs
            const updatedNodes = nodeStore.getAllNodes();
            console.log(`Rebuilt hierarchical IDs for ${updatedNodes.length} nodes:`);
            updatedNodes.forEach(node => {
                console.log(`Node ID: ${node.id}, Hierarchical ID: ${node.hierarchicalId}, Label: ${node.label}`);
            });

            // Explicitly center the view after loading demo
            // This is one case where we definitely want to center
            setTimeout(() => {
                console.log('[MVCPowerManager] Explicitly centering view after loading demo');
                handleCenterView();
            }, 200);

            // Show success message
            if (toast.current) {
                toast.current.show({
                    severity: 'success',
                    summary: 'Demo Loaded',
                    detail: 'Demo project loaded successfully',
                    life: 3000
                });
            }
        }).catch((error) => {
            console.error('Error loading demo:', error);

            // Show error message
            if (toast.current) {
                toast.current.show({
                    severity: 'error',
                    summary: 'Error',
                    detail: 'Failed to load demo project',
                    life: 5000
                });
            }
        });
    }, [setDemoMode, setCurrentProject, setTestRun, handleCenterView]);

    // Handle export project - opens export modal
    const handleExportProject = useCallback(() => {
        console.log('Opening export modal');
        setExportImportMode('export');
        setShowExportImportModal(true);
    }, []);

    // Handle import project - opens import modal
    const handleImportProject = useCallback(() => {
        console.log('Opening import modal');
        setExportImportMode('import');
        setShowExportImportModal(true);
    }, []);

    return (
        <SimpleLayout>
            <div className="power-manager">
                <Toast ref={toast} />

                {/* Top toolbar */}
                <div style={{
                    position: 'absolute',
                    top: 10,
                    left: 10,
                    right: 10,
                    zIndex: 1000,
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center'
                }}>
                    {/* Left side buttons */}
                    <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
                        {/* Home Icon */}
                        <button
                            onClick={() => window.location.href = '/'}
                            style={{
                                padding: '8px',
                                backgroundColor: '#4299e1',
                                color: 'white',
                                border: 'none',
                                borderRadius: '4px',
                                cursor: 'pointer',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center'
                            }}
                            title="Home"
                        >
                            🏠
                        </button>

                        {/* Load Project Button */}
                        <button
                            onClick={() => setShowProjectModal(true)}
                            style={{
                                padding: '5px 10px',
                                backgroundColor: '#4a5568',
                                color: 'white',
                                border: 'none',
                                borderRadius: '4px',
                                cursor: 'pointer',
                                display: 'flex',
                                alignItems: 'center',
                                gap: '5px'
                            }}
                            title="Load Project"
                        >
                            📁 {currentProject?.name || 'Load Project'}
                        </button>

                        {/* New Project Button */}
                        <button
                            onClick={handleNewProject}
                            style={{
                                padding: '5px 10px',
                                backgroundColor: '#48bb78',
                                color: 'white',
                                border: 'none',
                                borderRadius: '4px',
                                cursor: 'pointer',
                                display: 'flex',
                                alignItems: 'center',
                                gap: '5px'
                            }}
                            title="Create New Project"
                        >
                            ➕ New Project
                        </button>

                        {/* Load Demo Button */}
                        <button
                            onClick={handleLoadDemo}
                            style={{
                                padding: '5px 10px',
                                backgroundColor: '#ed8936',
                                color: 'white',
                                border: 'none',
                                borderRadius: '4px',
                                cursor: 'pointer'
                            }}
                            title="Load Demo Project"
                        >
                            Load Demo
                        </button>


                    </div>

                    {/* Right side buttons */}
                    <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
                        {/* Center View Button */}
                        <button
                            onClick={handleCenterView}
                            style={{
                                padding: '5px 10px',
                                backgroundColor: '#9f7aea',
                                color: 'white',
                                border: 'none',
                                borderRadius: '4px',
                                cursor: 'pointer'
                            }}
                            title="Center View"
                        >
                            Center View
                        </button>

                        {/* Manage Tags Button */}
                        <button
                            onClick={() => setShowTagManager(true)}
                            style={{
                                padding: '5px 10px',
                                backgroundColor: '#f6ad55',
                                color: 'white',
                                border: 'none',
                                borderRadius: '4px',
                                cursor: 'pointer'
                            }}
                            title="Manage Tags"
                        >
                            Manage Tags
                        </button>

                        {/* Import Button */}
                        <button
                            onClick={handleImportProject}
                            style={{
                                padding: '5px 10px',
                                backgroundColor: '#38a169',
                                color: 'white',
                                border: 'none',
                                borderRadius: '4px',
                                cursor: 'pointer'
                            }}
                            title="Import Project"
                        >
                            Import
                        </button>

                        {/* Export Button */}
                        <button
                            onClick={handleExportProject}
                            style={{
                                padding: '5px 10px',
                                backgroundColor: '#3182ce',
                                color: 'white',
                                border: 'none',
                                borderRadius: '4px',
                                cursor: 'pointer'
                            }}
                            title="Export Project"
                        >
                            Export
                        </button>

                        {/* Settings Button */}
                        <button
                            onClick={() => navigate('/settings')}
                            style={{
                                padding: '8px',
                                backgroundColor: '#718096',
                                color: 'white',
                                border: 'none',
                                borderRadius: '4px',
                                cursor: 'pointer',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center'
                            }}
                            title="Settings"
                        >
                            ⚙️
                        </button>

                        {/* User Profile */}
                        <div style={{ marginLeft: '10px' }}>
                            <UserProfile />
                        </div>

                    </div>
                </div>

                {/* Main content */}
                {/* No debug overlay in production */}

                {/* QuickSearch Bubble - handles both search and tag filtering */}
                {!quickSearchActive && (
                    <div className="quick-search-bubble-container-fixed">
                        <QuickSearchBubble
                            onActivate={() => {
                                setQuickSearchActive(true);
                                // Focus the search input after a short delay to ensure it's rendered
                                setTimeout(() => {
                                    if (searchInputRef.current) {
                                        searchInputRef.current.focus();
                                    }
                                }, 50);
                            }}
                            selectedTags={selectedTags}
                            onTagSelect={handleTagSelect}
                            onTagDeselect={handleTagDeselect}
                            onClearAllTags={() => {
                                setSelectedTags([]);
                                nodeHighlightService.setSelectedTags([]);
                                filterController.setTagFilter([], false);
                            }}
                        />
                    </div>
                )}

                {/* QuickSearch Section with TransformContext */}
                {quickSearchActive && (
                    <QuickSearchSectionWithTransform
                        searchQuery={searchQuery}
                        searchInputRef={searchInputRef}
                        setSearchQuery={(query) => {
                                setSearchQuery(query);

                                // Clear previous timeout
                                if (searchTimeoutRef.current) {
                                    clearTimeout(searchTimeoutRef.current);
                                }

                                // Set new timeout to avoid searching on every keystroke
                                searchTimeoutRef.current = setTimeout(() => {
                                    console.log('Searching for:', query);

                                    // Placeholder for search results
                                    const results: QuickSearchResult[] = [];

                                    // Get all nodes
                                    const allNodes = nodeStore.getAllNodes();
                                    const lowerQuery = query.toLowerCase();

                                    // Handle special syntax
                                    if (lowerQuery.startsWith('//')) {
                                        // Tag search with //
                                        const tagQuery = lowerQuery.substring(2).trim();
                                        console.log('Tag search query:', tagQuery);

                                        if (tagQuery) {
                                            // Search for tags by name
                                            const matchingTags = tags.filter(tag =>
                                                tag.name.toLowerCase().includes(tagQuery)
                                            );

                                            // Find nodes with matching tags
                                            for (const tag of matchingTags) {
                                                for (const node of allNodes) {
                                                    if (node.tags.includes(tag.id)) {
                                                        results.push({
                                                            id: node.id,
                                                            type: 'node',
                                                            title: node.title || 'Untitled',
                                                            description: `Tagged with: ${tag.name}`,
                                                            matchType: 'tag'
                                                        });
                                                    }
                                                }
                                            }
                                        }
                                    } else if (lowerQuery.startsWith('#')) {
                                        // Component search with #
                                        const componentQuery = lowerQuery.substring(1).trim();
                                        console.log('Component search query:', componentQuery);

                                        if (componentQuery) {
                                            const componentTypes = ['deadline', 'reminder', 'checklist', 'image', 'task', 'tasks', 'richtext', 'table'];
                                            const matchingTypes = componentTypes.filter(type =>
                                                type.includes(componentQuery) ||
                                                (type === 'tasks' && (componentQuery === 'checklist' || componentQuery === 'task'))
                                            );

                                            // Search for nodes with matching component types
                                            for (const componentType of matchingTypes) {
                                                for (const node of allNodes) {
                                                    if (node.segments && node.segments.some(segment =>
                                                        segment.type === componentType ||
                                                        (componentType === 'tasks' && (segment.type === 'checklist' || segment.type === 'task'))
                                                    )) {
                                                        results.push({
                                                            id: node.id,
                                                            type: 'node',
                                                            title: node.title || 'Untitled',
                                                            description: `Contains ${componentType}`,
                                                            matchType: 'component'
                                                        });
                                                    }
                                                }
                                            }
                                        }
                                    } else if (query.trim()) {
                                        // Regular search
                                        for (const node of allNodes) {
                                            // Search in title
                                            if (node.title.toLowerCase().includes(lowerQuery)) {
                                                results.push({
                                                    id: node.id,
                                                    type: 'node',
                                                    title: node.title || 'Untitled',
                                                    description: (node.content || 'No content').substring(0, 100),
                                                    matchType: 'title'
                                                });
                                                continue;
                                            }

                                            // Search in content
                                            if (node.content.toLowerCase().includes(lowerQuery)) {
                                                results.push({
                                                    id: node.id,
                                                    type: 'node',
                                                    title: node.title || 'Untitled',
                                                    description: (node.content || 'No content').substring(0, 100),
                                                    matchType: 'content'
                                                });
                                                continue;
                                            }

                                            // Search in segments
                                            if (node.segments) {
                                                for (const segment of node.segments) {
                                                    if (segment.content && segment.content.toLowerCase().includes(lowerQuery)) {
                                                        results.push({
                                                            id: segment.id.toString(),
                                                            type: 'segment',
                                                            title: `${segment.type} in ${node.title || 'Untitled'}`,
                                                            description: (segment.content || 'No content').substring(0, 100),
                                                            nodeId: node.id,
                                                            matchType: 'component'
                                                        });
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    setSearchResults(results);
                                    setSelectedResultIndex(0);
                                }, 300);
                            }}
                            closeSearch={() => {
                                setQuickSearchActive(false);
                                setSearchQuery('');
                                setSearchResults([]);
                            }}
                            searchResults={searchResults}
                            selectedResultIndex={selectedResultIndex}
                            setSelectedResultIndex={setSelectedResultIndex}
                            handleResultSelect={(result) => {
                                if (result.type === 'node') {
                                    setSelectedNodeId(result.id);
                                    setQuickSearchActive(false);
                                } else if (result.type === 'segment' && result.nodeId) {
                                    setSelectedNodeId(result.nodeId);
                                    setQuickSearchActive(false);
                                }
                            }}
                            selectedTags={selectedTags}
                            handleTagSelect={handleTagSelect}
                            handleTagDeselect={handleTagDeselect}
                        />
                )}

                {isKanbanView ? (
                    <div style={{ position: 'absolute', top: '60px', left: 0, right: 0, bottom: 0 }}>
                        <KanbanBoard
                            onNodeClick={(nodeId) => setSelectedNodeId(nodeId)}
                            selectedTags={selectedTags}
                            searchQuery={searchQuery}
                            searchResults={searchResults}
                        />
                    </div>
                ) : (
                    <div className="canvas-container" ref={canvasRef} style={{ position: 'absolute', top: '60px', left: 0, right: 0, bottom: 0 }}>
                        {/* Always using MVC architecture - legacy rendering removed */}
                        <GraphBridge
                            useLegacy={false}
                            width="100%"
                            height="calc(100vh - 60px)"
                            onNodeClick={(nodeId) => setSelectedNodeId(nodeId)}
                            onAddChild={handleAddChild}
                            backgroundColor={canvasBackgroundColor}
                        />

                        {/* Legacy InfiniteCanvas kept for reference but not used
                        <InfiniteCanvas
                            ref={infiniteCanvasRef}
                            onAddChild={handleAddChild}
                            onNodeClick={(nodeId) => setSelectedNodeId(nodeId)}
                            onDeleteNode={(nodeId, deleteWithChildren) => {
                                // Use the NodeController to delete the node with the appropriate option
                                nodeController.deleteNodeWithOptions(nodeId, { deleteSubtree: deleteWithChildren });
                            }}
                        />
                        */}
                    </div>
                )}



                {/* Node editor modal - always using MVC */}
                {selectedNodeId && (
                    <MVCNodeEditorModal
                        nodeId={selectedNodeId}
                        onClose={() => {
                            console.log('Closing node editor modal');
                            setSelectedNodeId(null);
                            setPendingComponentType(null);
                        }}
                        pendingComponentType={pendingComponentType}
                    />
                )}

                {/* Tag Manager */}
                {showTagManager && (
                    <div style={{
                        position: 'fixed',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        backgroundColor: 'rgba(0, 0, 0, 0.5)',
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        zIndex: 2000
                    }}>
                        <TagManager onClose={() => setShowTagManager(false)} />
                    </div>
                )}

                {/* Search Bar */}
                {showSearchBar && (
                    <div style={{
                        position: 'fixed',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        backgroundColor: 'rgba(0, 0, 0, 0.5)',
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        zIndex: 2000
                    }}>
                        <SearchBar
                            onNodeClick={(nodeId) => {
                                setSelectedNodeId(nodeId);
                                setShowSearchBar(false);
                            }}
                            onClose={() => setShowSearchBar(false)}
                        />
                    </div>
                )}

                {/* Project selection modal */}
                {showProjectModal && (
                    <ProjectSelectionModal
                        visible={showProjectModal && !loading}
                        onHide={() => setShowProjectModal(false)}
                        onCreateNewProject={handleNewProject}
                        onImport={handleImportProject}
                        onOpenProject={(projectId) => {
                            console.log('Opening project:', projectId);
                            // Load the selected project
                            loadProject(projectId);
                            setShowProjectModal(false);
                        }}
                        onLoadDemo={handleLoadDemo}
                    />
                )}

                {/* Dry run warning modal */}
                {showDryRunWarningModal && (
                    <DryRunWarningModal
                        visible={showDryRunWarningModal}
                        onHide={() => setShowDryRunWarningModal(false)}
                        onProceed={() => {
                            // Handle proceed with new project
                            setShowDryRunWarningModal(false);
                            handleNewProject();
                        }}
                        onExport={handleExportProject}
                        onUpgrade={() => {
                            // Handle upgrade - navigate to upgrade page
                            navigate('/upgrade');
                        }}
                        isExitingDryRun={false}
                    />
                )}

                {/* Keyboard shortcut handler */}
                <KeyboardShortcutHandler />

                {/* Shortcut help modal */}
                <ShortcutHelpModal
                    visible={showShortcutHelp}
                    onHide={() => setShowShortcutHelp(false)}
                />

                {/* Export/Import Modal */}
                <ExportImportModal
                    visible={showExportImportModal}
                    onHide={() => setShowExportImportModal(false)}
                    mode={exportImportMode}
                />

                {/* Migration component */}
                {showMigrationComponent && (
                    <MigrationComponent
                        onComplete={() => {
                            setShowMigrationComponent(false);
                            // Show success message
                            if (toast.current) {
                                toast.current.show({
                                    severity: 'success',
                                    summary: 'Success',
                                    detail: 'Migration to immutable node IDs completed successfully',
                                    life: 5000
                                });
                            }
                            // Center the view after migration
                            handleCenterView();
                        }}
                    />
                )}

                {/* Bottom Navigation */}
                <div style={{
                    position: 'fixed',
                    bottom: 0,
                    left: '50%',
                    transform: 'translateX(-50%)',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    zIndex: 1000,
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    borderRadius: '12px 12px 0 0',
                    padding: '8px 16px 4px 16px',
                    boxShadow: '0 -2px 10px rgba(0, 0, 0, 0.1)',
                    backdropFilter: 'blur(10px)'
                }}>
                    <div style={{
                        display: 'flex',
                        gap: '0px'
                    }}>
                        <button
                            onClick={() => setIsKanbanView(false)}
                            style={{
                                padding: '8px 16px',
                                backgroundColor: 'transparent',
                                color: !isKanbanView ? '#4299e1' : '#718096',
                                border: 'none',
                                borderRadius: '8px',
                                cursor: 'pointer',
                                fontSize: '14px',
                                fontWeight: !isKanbanView ? '600' : '400',
                                transition: 'all 0.2s ease'
                            }}
                        >
                            Graph
                        </button>
                        <button
                            onClick={() => setIsKanbanView(true)}
                            style={{
                                padding: '8px 16px',
                                backgroundColor: 'transparent',
                                color: isKanbanView ? '#4299e1' : '#718096',
                                border: 'none',
                                borderRadius: '8px',
                                cursor: 'pointer',
                                fontSize: '14px',
                                fontWeight: isKanbanView ? '600' : '400',
                                transition: 'all 0.2s ease'
                            }}
                        >
                            Kanban
                        </button>
                    </div>
                    {/* Active indicator line */}
                    <div style={{
                        width: '60px',
                        height: '2px',
                        backgroundColor: '#4299e1',
                        borderRadius: '1px',
                        transform: isKanbanView ? 'translateX(32px)' : 'translateX(-32px)',
                        transition: 'transform 0.2s ease',
                        marginTop: '4px'
                    }} />
                </div>
            </div>
        </SimpleLayout>
    );
};

export default MVCPowerManager;
