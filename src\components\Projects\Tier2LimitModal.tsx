import React from 'react';
import { Dialog } from 'primereact/dialog';
import { Button } from 'primereact/button';

interface Tier2LimitModalProps {
  visible: boolean;
  onHide: () => void;
  onUpgrade: () => void;
  onDryRunMode: () => void;
}

const Tier2LimitModal: React.FC<Tier2LimitModalProps> = ({
  visible,
  onHide,
  onUpgrade,
  onDryRunMode
}) => {
  return (
    <Dialog
      header="Project Limit Reached"
      visible={visible}
      onHide={onHide}
      style={{ width: '500px' }}
      modal
      blockScroll={true}
      dismissableMask={false}
      closeOnEscape={false}
      closable={true}
    >
      <div style={{ padding: '1rem 0' }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          marginBottom: '1rem',
          padding: '1rem',
          backgroundColor: '#fff3cd',
          border: '1px solid #ffeaa7',
          borderRadius: '4px'
        }}>
          <i className="pi pi-exclamation-triangle" style={{
            fontSize: '1.5rem',
            color: '#856404',
            marginRight: '1rem'
          }}></i>
          <div>
            <h4 style={{ margin: '0 0 0.5rem 0', color: '#856404' }}>Free Account Limit</h4>
            <p style={{ margin: 0, color: '#856404' }}>
              Free accounts are limited to <strong>1 project</strong>. You have reached this limit.
            </p>
          </div>
        </div>

        <p style={{ marginBottom: '1.5rem' }}>
          You have two options to continue:
        </p>

        <div style={{ marginBottom: '1.5rem' }}>
          <h5 style={{ marginBottom: '0.5rem' }}>Option 1: Upgrade Your Account</h5>
          <p style={{ marginBottom: '1rem', color: '#666' }}>
            Upgrade to a paid account to create unlimited projects and access premium features.
          </p>

          <h5 style={{ marginBottom: '0.5rem' }}>Option 2: Use Dry Run Mode</h5>
          <p style={{ marginBottom: '0', color: '#666' }}>
            Create and work on projects without saving to your account. You can export your work when finished.
          </p>
        </div>

        <div style={{
          display: 'flex',
          gap: '1rem',
          justifyContent: 'flex-end',
          marginTop: '2rem'
        }}>
          <Button
            label="Cancel"
            icon="pi pi-times"
            className="p-button-text"
            onClick={onHide}
          />
          <Button
            label="Use Dry Run Mode"
            icon="pi pi-play"
            className="p-button-outlined"
            onClick={onDryRunMode}
          />
          <Button
            label="Upgrade Account"
            icon="pi pi-arrow-up"
            className="p-button-success"
            onClick={onUpgrade}
          />
        </div>
      </div>
    </Dialog>
  );
};

export default Tier2LimitModal;
