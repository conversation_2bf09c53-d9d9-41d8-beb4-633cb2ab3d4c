/**
 * NodeHighlightService - Manages node highlighting based on tag selection
 *
 * This service provides centralized management of node highlighting state
 * based on selected tags, allowing components to react to highlight changes.
 */

import { EventEmitter } from '../model/EventEmitter';
import { nodeStore } from '../model/NodeStore';

export enum NodeHighlightEvents {
  HIGHLIGHT_CHANGED = 'node:highlight:changed'
}

export class NodeHighlightService extends EventEmitter {
  private selectedTags: string[] = [];
  private selectedTagsData: Array<{id: string, color: string}> = [];
  private highlightedNodeCache: Map<string, boolean> = new Map();

  constructor() {
    super();
  }

  /**
   * Set the currently selected tags for highlighting
   * @param tags Array of tag IDs
   * @param tagsData Optional array of tag data with colors
   */
  setSelectedTags(tags: string[], tagsData?: Array<{id: string, color: string}>): void {
    console.log('[NodeHighlightService] Setting selected tags:', tags);
    this.selectedTags = [...tags];
    this.selectedTagsData = tagsData ? [...tagsData] : [];
    // Clear cache when tags change
    this.highlightedNodeCache.clear();
    this.emit(NodeHighlightEvents.HIGHLIGHT_CHANGED, this.selectedTags);
    console.log('[NodeHighlightService] Emitted HIGHLIGHT_CHANGED event');
  }

  /**
   * Get the currently selected tags
   * @returns Array of tag IDs
   */
  getSelectedTags(): string[] {
    return [...this.selectedTags];
  }

  /**
   * Check if a node should be highlighted
   * @param nodeId The node ID to check
   * @returns True if the node should be highlighted
   */
  isNodeHighlighted(nodeId: string): boolean {
    // Check cache first
    if (this.highlightedNodeCache.has(nodeId)) {
      return this.highlightedNodeCache.get(nodeId)!;
    }

    // If no tags are selected, no nodes are highlighted
    if (this.selectedTags.length === 0) {
      this.highlightedNodeCache.set(nodeId, false);
      return false;
    }

    const node = nodeStore.getNode(nodeId);
    if (!node) {
      this.highlightedNodeCache.set(nodeId, false);
      return false;
    }

    // Node is highlighted if it has ANY of the selected tags
    const isHighlighted = this.selectedTags.some(tagId => node.tags.includes(tagId));
    this.highlightedNodeCache.set(nodeId, isHighlighted);
    return isHighlighted;
  }

  /**
   * Check if any tags are currently selected
   * @returns True if tags are selected
   */
  hasSelectedTags(): boolean {
    return this.selectedTags.length > 0;
  }

  /**
   * Get the highlight color for a node based on its tags
   * @param nodeId The node ID to check
   * @returns The color to use for highlighting, or null if not highlighted
   */
  getNodeHighlightColor(nodeId: string): string | null {
    if (!this.isNodeHighlighted(nodeId)) {
      return null;
    }

    const node = nodeStore.getNode(nodeId);
    if (!node) {
      return null;
    }

    // Find the first matching tag color
    for (const tagId of this.selectedTags) {
      if (node.tags.includes(tagId)) {
        const tagData = this.selectedTagsData.find(t => t.id === tagId);
        if (tagData) {
          return tagData.color;
        }
      }
    }

    // Fallback to default blue if no color found
    return '#4299e1';
  }

  /**
   * Clear all selected tags
   */
  clearSelectedTags(): void {
    this.setSelectedTags([]);
  }

  /**
   * Add a tag to the selection
   * @param tagId The tag ID to add
   */
  addSelectedTag(tagId: string): void {
    if (!this.selectedTags.includes(tagId)) {
      this.setSelectedTags([...this.selectedTags, tagId]);
    }
  }

  /**
   * Remove a tag from the selection
   * @param tagId The tag ID to remove
   */
  removeSelectedTag(tagId: string): void {
    this.setSelectedTags(this.selectedTags.filter(id => id !== tagId));
  }
}

// Create a singleton instance
export const nodeHighlightService = new NodeHighlightService();
