import React, { useState, useRef } from 'react';
import { Dialog } from 'primereact/dialog';
import { But<PERSON> } from 'primereact/button';
import { ProgressBar } from 'primereact/progressbar';
import { RadioButton } from 'primereact/radiobutton';
import { Toast } from 'primereact/toast';
import { useGraph } from '../context/GraphContext';
import { useAuth } from '../context/AuthContext';
import { useAuthWrapper } from '../context/AuthWrapper';
import { useProject } from '../context/ProjectContext';
import { useTestRun } from '../context/TestRunContext';
import { useTags } from '../context/TagContext';
import { TierLevel } from '../services/userProfileService';
import { readJsonFile, ExportedGraphData } from '../utils/exportImport';
import { DocumentExportOptions } from '../utils/documentConverter';
import { Project, saveProjectData } from '../services/projectService';
import './ExportImportModal.css';

interface ExportImportModalProps {
  visible: boolean;
  onHide: () => void;
  mode: 'export' | 'import';
}

const ExportImportModal: React.FC<ExportImportModalProps> = ({ visible, onHide, mode }) => {
  const { exportGraph, importGraph, nodes } = useGraph();
  const { tierLevel, isAdmin, isPaid } = useAuth();
  const { isAuthenticated } = useAuthWrapper();
  const { createNewProject, saveCurrentProject, setCurrentProject, projects, loadProject } = useProject();
  const { setTestRun } = useTestRun();
  const { setImportedTags } = useTags();
  const [fileName, setFileName] = useState('graph-export');
  const [fileFormat, setFileFormat] = useState<'json' | 'docx' | 'pdf'>('json');
  // Simplified import - always replace
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const toastRef = useRef<Toast>(null);

  // State for duplicate project handling
  const [showDuplicateDialog, setShowDuplicateDialog] = useState(false);
  const [duplicateProjectData, setDuplicateProjectData] = useState<{
    projectName: string;
    existingProject: Project | null;
    importedData: ExportedGraphData | null;
  }>({ projectName: '', existingProject: null, importedData: null });

  // Export options
  const [exportOptions] = useState<DocumentExportOptions>({
    title: 'Graph Export',
    author: 'PowerManager',
    includeImages: true,
    includeFormatting: true
  });

  // Helper function to find the root node and get its title
  const getRootNodeTitle = (importedData: ExportedGraphData): string => {
    // Find the root node (node without a parent)
    const rootNode = importedData.nodes.find(node => !node.relationships?.parentId);
    if (rootNode && rootNode.title) {
      return rootNode.title;
    }
    // Default name if no root node is found
    return 'Imported Project';
  };

  // Helper function to check if a project with the same name already exists
  const checkForDuplicateProject = (projectName: string): Project | null => {
    if (!projects || projects.length === 0) return null;

    // Find a project with the same name (case insensitive)
    return projects.find(project =>
      project.name.toLowerCase() === projectName.toLowerCase()
    ) || null;
  };

  // Helper function to generate a unique project name
  const generateUniqueProjectName = (baseName: string): string => {
    if (!checkForDuplicateProject(baseName)) return baseName;

    let counter = 1;
    let newName = `${baseName} (${counter})`;

    // Keep incrementing the counter until we find a unique name
    while (checkForDuplicateProject(newName)) {
      counter++;
      newName = `${baseName} (${counter})`;
    }

    return newName;
  };

  // Helper function to handle duplicate project names
  const handleDuplicateProject = (projectName: string, importedData: ExportedGraphData) => {
    const existingProject = checkForDuplicateProject(projectName);

    if (existingProject) {
      // Store the data and show the duplicate dialog
      setDuplicateProjectData({
        projectName,
        existingProject,
        importedData
      });
      setShowDuplicateDialog(true);
      return true; // Duplicate found
    }

    return false; // No duplicate found
  };

  // Helper function to overwrite an existing project
  const overwriteExistingProject = async (projectId: string, importedData: ExportedGraphData): Promise<boolean> => {
    try {
      // Step 1: Verify the project exists
      console.log('Verifying project exists for overwrite:', projectId);
      const project = projects.find(p => p.id === projectId);
      if (!project) {
        throw new Error('Project not found for overwrite');
      }

      // Step 2: Save the imported data directly to the project storage
      // This bypasses the graph state and saves directly to the database
      console.log('Overwriting existing project data:', projectId);
      const saveSuccess = await saveProjectData(
        projectId,
        importedData.nodes || [],
        importedData.edges || [],
        project.name,
        {
          nodes: importedData.nodes || [],
          edges: importedData.edges || [],
          tags: importedData.tags || [],
          version: '1.0',
          metadata: {
            projectId: project.id,
            projectName: project.name,
            lastSaved: new Date().toISOString()
          }
        }
      );

      if (!saveSuccess) {
        console.error('Failed to overwrite existing project data');
        return false;
      }

      console.log('Successfully overwrote existing project data');

      // Step 3: Load the project to make it the current project
      console.log('Loading the overwritten project:', projectId);
      const loadSuccess = await loadProject(projectId);

      if (!loadSuccess) {
        console.error('Failed to load the overwritten project');
        // We don't return false here because the data was saved successfully
        // The user can still access the project from the project list
      } else {
        console.log('Successfully loaded the overwritten project');
      }

      return true;
    } catch (error) {
      console.error('Error overwriting existing project:', error);
      return false;
    }
  };

  // Helper function to save imported project for Tier 0 and Tier 1 users
  const saveImportedProject = async (importedData: ExportedGraphData): Promise<boolean> => {
    try {
      // Get project name from root node title
      const projectName = getRootNodeTitle(importedData);

      // Check if a project with this name already exists
      if (handleDuplicateProject(projectName, importedData)) {
        // If duplicate exists, the dialog will be shown and we'll handle it there
        return true;
      }

      // Step 1: Create a new project in the database
      console.log('Creating new project for import:', projectName);
      const newProject = await createNewProject(projectName, 'Imported project');
      if (!newProject) {
        throw new Error('Failed to create project for imported data');
      }

      // Step 2: Save the imported data directly to the project storage
      // This bypasses the graph state and saves directly to the database
      console.log('Saving imported data directly to project storage:', newProject.id);
      const saveSuccess = await saveProjectData(
        newProject.id,
        importedData.nodes || [],
        importedData.edges || [],
        newProject.name,
        {
          nodes: importedData.nodes || [],
          edges: importedData.edges || [],
          tags: importedData.tags || [],
          version: '1.0',
          metadata: {
            projectId: newProject.id,
            projectName: newProject.name,
            lastSaved: new Date().toISOString()
          }
        }
      );

      if (!saveSuccess) {
        console.error('Failed to save imported project data');
        return false;
      }

      console.log('Successfully saved imported project data');

      // Step 3: Load the project to make it the current project
      console.log('Loading the newly imported project:', newProject.id);
      const loadSuccess = await loadProject(newProject.id);

      if (!loadSuccess) {
        console.error('Failed to load the imported project');
        // We don't return false here because the data was saved successfully
        // The user can still access the project from the project list
      } else {
        console.log('Successfully loaded the imported project');
      }

      return true;
    } catch (error) {
      console.error('Error saving imported project:', error);
      return false;
    }
  };

  const handleExport = async () => {
    try {
      setIsProcessing(true);
      setError(null);

      // Set export options
      const options: DocumentExportOptions = {
        ...exportOptions,
        title: exportOptions.title || fileName || 'Graph Export'
      };

      // Export with the selected format
      await exportGraph(fileName, fileFormat, options);

      setSuccess(`Graph exported successfully as ${fileFormat.toUpperCase()} file!`);

      // Hide the dialog after a short delay to show the success message
      // This is a UI enhancement, not a critical timing issue
      setTimeout(() => onHide(), 1500);
    } catch (error) {
      console.error('Export error:', error);
      setError(`Failed to export graph: ${error instanceof Error ? error.message : 'Unknown error'}. Please try again.`);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleImport = async () => {
    if (!fileInputRef.current?.files?.length) {
      setError('Please select a file to import.');
      return;
    }

    const file = fileInputRef.current.files[0];

    try {
      setIsProcessing(true);
      setError(null);

      // Validate file size
      if (file.size > 10 * 1024 * 1024) { // 10MB limit
        throw new Error('File is too large. Maximum size is 10MB.');
      }

      // Validate file type
      const fileExtension = file.name.split('.').pop()?.toLowerCase();
      if (!fileExtension || fileExtension !== 'json') {
        throw new Error('Only JSON files are supported for import.');
      }

      // Read the file to get the data
      const jsonData = await readJsonFile(file);

      // Handle based on user tier
      if (isAuthenticated && (isAdmin || isPaid)) {
        // For Tier 0 (Admin) and Tier 1 (Paid) users, save as a new project
        console.log('Tier 0/1 user detected, saving imported project directly');

        // Don't import into the current graph, just save the data directly
        const saveSuccess = await saveImportedProject(jsonData);

        // Process imported tags
        if (jsonData.tags && jsonData.tags.length > 0) {
          console.log('Processing imported tags:', jsonData.tags);
          // Process tags to ensure they have readable names
          const processedTags = jsonData.tags.map(tag => {
            // If it's a UUID-style tag, give it a better name
            if (tag.id.includes('-') && tag.id.length > 20) {
              if (tag.id === 'tag-6e05f5c8-08d5-43cf-873f-3c9f465b3fa6') {
                return { ...tag, name: 'IMPORTANT' };
              } else if (tag.id.startsWith('tag-ui-')) {
                return { ...tag, name: 'UI' };
              } else {
                return { ...tag, name: 'CUSTOM TAG' };
              }
            }
            return tag;
          });
          setImportedTags(processedTags);
        }

        if (saveSuccess) {
          setSuccess('Project imported and saved successfully!');
        } else {
          console.error('Failed to save imported project');
          setSuccess('Project imported but could not be saved automatically.');
        }
      } else {
        // For Tier 2 (Free) users or unauthenticated users, import into the current graph
        console.log('Tier 2 or unsigned user detected, importing into current graph');

        // Import into the current graph
        const importResult = await importGraph(file, false, true);

        // Process imported tags
        if (importResult.tags && importResult.tags.length > 0) {
          console.log('Processing imported tags:', importResult.tags);
          // Process tags to ensure they have readable names
          const processedTags = importResult.tags.map(tag => {
            // If it's a UUID-style tag, give it a better name
            if (tag.id.includes('-') && tag.id.length > 20) {
              if (tag.id === 'tag-6e05f5c8-08d5-43cf-873f-3c9f465b3fa6') {
                return { ...tag, name: 'IMPORTANT' };
              } else if (tag.id.startsWith('tag-ui-')) {
                return { ...tag, name: 'UI' };
              } else {
                return { ...tag, name: 'CUSTOM TAG' };
              }
            }
            return tag;
          });
          setImportedTags(processedTags);
        }

        // Enter Dry Run mode
        setTestRun(true);
        setSuccess('Project imported successfully in Dry Run mode!');
      }

      // Hide the dialog after a short delay to show the success message
      // This is a UI enhancement, not a critical timing issue
      setTimeout(() => onHide(), 1500);
    } catch (error) {
      console.error('Import error:', error);
      setError(`Failed to import graph: ${error instanceof Error ? error.message : 'Invalid file format'}. Please ensure the file is a valid graph export.`);
    } finally {
      setIsProcessing(false);
    }
  };

  const renderExportContent = () => {
    // Get file extension based on format
    const getExtension = () => {
      switch (fileFormat) {
        case 'docx': return '.docx';
        case 'pdf': return '.pdf';
        case 'json': default: return '.json';
      }
    };

    return (
      <div className="export-container">
        <div className="p-field format-selection">
          <label className="format-label">Select File Format</label>
          <div className="format-options">
            <div className="p-field-radiobutton">
              <RadioButton
                inputId="format-json"
                name="fileFormat"
                value="json"
                onChange={(e) => setFileFormat(e.value)}
                checked={fileFormat === 'json'}
                disabled={isProcessing}
              />
              <label htmlFor="format-json">JSON</label>
            </div>
            <div className="p-field-radiobutton">
              <RadioButton
                inputId="format-docx"
                name="fileFormat"
                value="docx"
                onChange={(e) => setFileFormat(e.value)}
                checked={fileFormat === 'docx'}
                disabled={isProcessing}
              />
              <label htmlFor="format-docx">Word (DOCX)</label>
            </div>
            <div className="p-field-radiobutton">
              <RadioButton
                inputId="format-pdf"
                name="fileFormat"
                value="pdf"
                onChange={(e) => setFileFormat(e.value)}
                checked={fileFormat === 'pdf'}
                disabled={isProcessing}
              />
              <label htmlFor="format-pdf">PDF</label>
            </div>
          </div>
        </div>

        <div className="p-field">
          <label htmlFor="fileName">File Name</label>
          <div className="file-name-container">
            <input
              id="fileName"
              type="text"
              value={fileName}
              onChange={(e) => setFileName(e.target.value)}
              className="p-inputtext p-component"
              disabled={isProcessing}
            />
            <span className="file-extension">{getExtension()}</span>
          </div>
          <small className="file-name-hint">The file will be saved with the {getExtension()} extension</small>
        </div>

        <div className="export-info">
          <p>
            {fileFormat === 'json' ? (
              'This will export all nodes and their components (including images, tables, etc.) as a JSON file that can be imported later.'
            ) : fileFormat === 'docx' ? (
              'This will export your node structure as a Word document with headings and content based on your node hierarchy.'
            ) : (
              'This will export your node structure as a PDF document with headings and content based on your node hierarchy.'
            )}
          </p>
          <p className="note-text">
            <i className="pi pi-info-circle"></i> Note: When the save dialog appears, the file will already have the correct extension. Please do not change the extension.
          </p>
        </div>
      </div>
    );
  };

  const renderImportContent = () => (
    <div className="import-container">
      <div className="p-field">
        <label htmlFor="fileUpload">Select File</label>
        <input
          ref={fileInputRef}
          id="fileUpload"
          type="file"
          accept=".json"
          className="p-inputtext p-component"
          disabled={isProcessing}
        />
      </div>

      <div className="import-info">
        <p>
          Importing will replace all existing nodes with the ones from the selected file.
          Currently, only JSON files exported from this application are supported.
        </p>
        <p className="warning-text">
          <i className="pi pi-exclamation-triangle"></i> Warning: This will delete all your current nodes and cannot be undone!
        </p>

        {isAuthenticated && (isAdmin || isPaid) ? (
          <div className="tier-info">
            <p className="success-text">
              <i className="pi pi-check-circle"></i> As a {isAdmin ? 'Tier 0 (Admin)' : 'Tier 1 (Paid)'} user, your imported project will be automatically saved as a new project.
            </p>
          </div>
        ) : (
          <div className="tier-info">
            <p className="info-text">
              <i className="pi pi-info-circle"></i> {isAuthenticated ? 'As a Tier 2 (Free) user' : 'As an unsigned user'}, your imported project will be loaded in Dry Run mode and will not be saved automatically.
            </p>
          </div>
        )}

        <div className="supported-formats">
          <h4>Supported File Format:</h4>
          <ul>
            <li><strong>JSON</strong> - Full graph export with all node data and relationships</li>
          </ul>
        </div>
      </div>
    </div>
  );

  // Handle overwrite existing project
  const handleOverwriteProject = async () => {
    if (!duplicateProjectData.existingProject || !duplicateProjectData.importedData) {
      setShowDuplicateDialog(false);
      return;
    }

    setIsProcessing(true);
    setShowDuplicateDialog(false);

    try {
      // Overwrite the existing project
      const success = await overwriteExistingProject(
        duplicateProjectData.existingProject.id,
        duplicateProjectData.importedData
      );

      if (success) {
        setSuccess(`Project "${duplicateProjectData.projectName}" has been overwritten successfully!`);
      } else {
        setError(`Failed to overwrite project "${duplicateProjectData.projectName}".`);
      }

      // Reset duplicate data
      setDuplicateProjectData({ projectName: '', existingProject: null, importedData: null });

      // Hide the dialog after a short delay to show the success message
      // This is a UI enhancement, not a critical timing issue
      setTimeout(() => onHide(), 1500);
    } catch (error) {
      console.error('Error in handleOverwriteProject:', error);
      setError(`Failed to overwrite project: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle create new project with a unique name
  const handleCreateNewWithUniqueName = async () => {
    if (!duplicateProjectData.projectName || !duplicateProjectData.importedData) {
      setShowDuplicateDialog(false);
      return;
    }

    setIsProcessing(true);
    setShowDuplicateDialog(false);

    try {
      // Generate a unique name
      const uniqueName = generateUniqueProjectName(duplicateProjectData.projectName);

      // Create a new project with the unique name
      const newProject = await createNewProject(uniqueName, 'Imported project');
      if (!newProject) {
        throw new Error(`Failed to create project "${uniqueName}"`);
      }

      console.log('Created new project with unique name:', uniqueName);

      // Save the imported data directly to the project storage
      // This bypasses the graph state and saves directly to the database
      console.log('Saving imported data to the new project:', newProject.id);
      const saveSuccess = await saveProjectData(
        newProject.id,
        duplicateProjectData.importedData.nodes || [],
        duplicateProjectData.importedData.edges || [],
        newProject.name,
        {
          nodes: duplicateProjectData.importedData.nodes || [],
          edges: duplicateProjectData.importedData.edges || [],
          tags: duplicateProjectData.importedData.tags || [],
          version: '1.0',
          metadata: {
            projectId: newProject.id,
            projectName: newProject.name,
            lastSaved: new Date().toISOString()
          }
        }
      );

      // Process imported tags
      if (duplicateProjectData.importedData.tags && duplicateProjectData.importedData.tags.length > 0) {
        console.log('Processing imported tags for new project:', duplicateProjectData.importedData.tags);
        // Process tags to ensure they have readable names
        const processedTags = duplicateProjectData.importedData.tags.map(tag => {
          // If it's a UUID-style tag, give it a better name
          if (tag.id.includes('-') && tag.id.length > 20) {
            if (tag.id === 'tag-6e05f5c8-08d5-43cf-873f-3c9f465b3fa6') {
              return { ...tag, name: 'IMPORTANT' };
            } else if (tag.id.startsWith('tag-ui-')) {
              return { ...tag, name: 'UI' };
            } else {
              return { ...tag, name: 'CUSTOM TAG' };
            }
          }
          return tag;
        });
        setImportedTags(processedTags);
      }

      if (saveSuccess) {
        // Load the project to make it the current project
        console.log('Loading the newly created project:', newProject.id);
        const loadSuccess = await loadProject(newProject.id);

        if (loadSuccess) {
          setSuccess(`Project imported and saved as "${uniqueName}" successfully!`);
        } else {
          setSuccess(`Project saved as "${uniqueName}" but could not be loaded automatically. Please select it from your projects list.`);
        }
      } else {
        setError(`Failed to save project as "${uniqueName}".`);
      }

      // Reset duplicate data
      setDuplicateProjectData({ projectName: '', existingProject: null, importedData: null });

      // Hide the dialog after a short delay to show the success message
      // This is a UI enhancement, not a critical timing issue
      setTimeout(() => onHide(), 1500);
    } catch (error) {
      console.error('Error in handleCreateNewWithUniqueName:', error);
      setError(`Failed to create project: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsProcessing(false);
    }
  };

  // Render the duplicate project dialog
  const renderDuplicateProjectDialog = () => {
    if (!duplicateProjectData.existingProject) return null;

    return (
      <Dialog
        header="Project Name Already Exists"
        visible={showDuplicateDialog}
        onHide={() => setShowDuplicateDialog(false)}
        style={{ width: '450px' }}
        modal
        blockScroll={true}
        dismissableMask={false}
        closeOnEscape={false}
        footer={
          <div>
            <Button
              label="Cancel"
              icon="pi pi-times"
              onClick={() => setShowDuplicateDialog(false)}
              className="p-button-text"
            />
            <Button
              label="Create New"
              icon="pi pi-plus"
              onClick={handleCreateNewWithUniqueName}
              className="p-button-secondary"
            />
            <Button
              label="Overwrite"
              icon="pi pi-sync"
              onClick={handleOverwriteProject}
              className="p-button-danger"
            />
          </div>
        }
      >
        <div className="duplicate-project-dialog">
          <p>
            A project with the name <strong>"{duplicateProjectData.projectName}"</strong> already exists.
          </p>
          <p>What would you like to do?</p>
          <ul>
            <li><strong>Overwrite:</strong> Replace the existing project with the imported one</li>
            <li><strong>Create New:</strong> Create a new project with a unique name</li>
            <li><strong>Cancel:</strong> Cancel the import operation</li>
          </ul>
        </div>
      </Dialog>
    );
  };

  const renderFooter = () => (
    <div>
      <Button
        label="Cancel"
        icon="pi pi-times"
        onClick={onHide}
        className="p-button-text"
        disabled={isProcessing}
      />
      <Button
        label={mode === 'export' ? 'Export' : 'Import'}
        icon={mode === 'export' ? 'pi pi-upload' : 'pi pi-download'}
        onClick={mode === 'export' ? handleExport : handleImport}
        autoFocus
        disabled={isProcessing}
      />
    </div>
  );

  return (
    <>
      <Toast ref={toastRef} />

      {/* Duplicate project dialog */}
      {renderDuplicateProjectDialog()}

      <Dialog
        header={mode === 'export' ? 'Export Graph' : 'Import Graph'}
        visible={visible}
        style={{ width: '500px' }}
        onHide={onHide}
        footer={renderFooter()}
        modal
        blockScroll={true}
        dismissableMask={false}
        closable={!isProcessing}
        closeOnEscape={!isProcessing}
      >
        {isProcessing && (
          <div className="progress-container">
            <ProgressBar mode="indeterminate" style={{ height: '6px' }} />
            <p>Processing... Please wait.</p>
          </div>
        )}

        {error && (
          <div className="error-message">
            <i className="pi pi-exclamation-triangle" /> {error}
          </div>
        )}

        {success && (
          <div className="success-message">
            <i className="pi pi-check" /> {success}
          </div>
        )}

        {mode === 'export' ? renderExportContent() : renderImportContent()}
      </Dialog>
    </>
  );
};

export default ExportImportModal;
