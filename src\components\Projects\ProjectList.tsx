import React, { useEffect, useState } from 'react';
import { useProject } from '../../context/ProjectContext';
import { useGraph } from '../../context/GraphContext';
import { useAuth } from '../../context/AuthContext';
import { getProjectData, saveProjectData } from '../../services/projectService';
import { TierLevel, getMaxProjectsAllowed } from '../../services/userProfileService';
import { Button } from 'primereact/button';
import { Dialog } from 'primereact/dialog';
import { InputText } from 'primereact/inputtext';
import { InputTextarea } from 'primereact/inputtextarea';
import { ConfirmDialog, confirmDialog } from 'primereact/confirmdialog';
import { Toast } from 'primereact/toast';
import { useRef } from 'react';
import { calculateGraphLayout } from '../../utils/graphLayout';
import { GraphLayout } from '../../types/layouts';
import { CanvasNode, EditorSegment, CanvasEdge } from '../../types';
import { nodeController } from '../../controller/NodeController';
import './ProjectList.css';

// Global reference to the function that shows the new project dialog
// This allows other components to trigger the dialog directly
let globalShowNewProjectDialog: (() => void) | null = null;

const ProjectList: React.FC = () => {
  const {
    projects,
    currentProject,
    loadProjects,
    loadProject,
    createNewProject,
    deleteCurrentProject,
    setCurrentProject,
    setDemoMode,
    saveCurrentProject,
    error: projectError
  } = useProject();
  const { nodes, edges, setNodes, setEdges } = useGraph();
  const { isAuthenticated, user, tierLevel, tierName, isPaid, isFree } = useAuth();
  const [showNewProjectDialog, setShowNewProjectDialog] = useState(false);
  const [newProjectName, setNewProjectName] = useState('');
  const [newProjectDescription, setNewProjectDescription] = useState('');
  const [loading, setLoading] = useState(false);
  const toast = useRef<Toast>(null);

  useEffect(() => {
    if (isAuthenticated) {
      loadProjects();
    }
  }, [isAuthenticated, loadProjects]);

  // Set the global function to show the new project dialog
  useEffect(() => {
    // Set the global function to show the new project dialog
    globalShowNewProjectDialog = () => {
      console.log('Global function called to show new project dialog');
      setShowNewProjectDialog(true);
    };

    // Clean up when component unmounts
    return () => {
      globalShowNewProjectDialog = null;
    };
  }, []);

  // Log project error when it changes
  useEffect(() => {
    if (projectError) {
      console.log('Project error detected in ProjectList:', projectError);
    }
  }, [projectError]);

  const handleProjectClick = async (projectId: string) => {
    // Reset demo mode when clicking on a project
    setDemoMode(false);

    try {
      setLoading(true);
      console.log('Loading project with ID:', projectId);

      // Get the project metadata
      const project = projects.find(p => p.id === projectId);
      if (!project) {
        throw new Error('Project not found');
      }

      // Use the loadProject function from ProjectContext to properly load the project
      // This ensures all the proper cleanup and setup is done
      const success = await loadProject(projectId);
      if (!success) {
        throw new Error('Failed to load project');
      }

      console.log('Project loaded successfully');

      // Check if the project has nodes
      if (nodes.length === 0) {
        // If no nodes, create a root node with the project name
        console.log('Project has no nodes, creating root node with project name:', project.name);
        createRootNodeWithProjectName(project.name);

        // Save the project with the root node
        console.log('Saving project with root node...');
        const saveSuccess = await saveCurrentProject();
        console.log('Project saved successfully:', saveSuccess);
      } else if (nodes.length === 1) {
        // CRITICAL FIX: Handle old projects with only a root node
        const rootNode = nodes[0];
        if (!rootNode.relationships.parentId && rootNode.relationships.childIds.length === 0) {
          console.log('Old project detected with only root node, adding two child nodes...');

          // Add two child nodes to the existing root
          const childNode1Id = nodeController.addChild(rootNode.id, 'richtext');
          const childNode2Id = nodeController.addChild(rootNode.id, 'richtext');

          // Update the child node titles
          if (childNode1Id) {
            nodeController.updateNode(childNode1Id, { title: 'Node 1' });
          }
          if (childNode2Id) {
            nodeController.updateNode(childNode2Id, { title: 'Node 2' });
          }

          console.log('Added child nodes to old project root');

          // Save the updated project
          const saveSuccess = await saveCurrentProject();
          console.log('Updated old project saved successfully:', saveSuccess);
        }
      }

      // Dispatch a custom event to center the view
      window.dispatchEvent(new CustomEvent('project-loaded'));
    } catch (error) {
      console.error('Error loading project:', error);
      toast.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: error instanceof Error ? error.message : 'Failed to load project'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCreateProject = async () => {
    // Reset demo mode when creating a new project
    setDemoMode(false);

    if (!newProjectName.trim()) {
      toast.current?.show({
        severity: 'warn',
        summary: 'Warning',
        detail: 'Project name is required'
      });
      return;
    }

    try {
      setLoading(true);
      console.log('Creating new project:', newProjectName);
      const newProject = await createNewProject(newProjectName, newProjectDescription);

      if (!newProject) {
        throw new Error('Failed to create project');
      }

      console.log('Project created successfully:', newProject);
      setShowNewProjectDialog(false);
      setNewProjectName('');
      setNewProjectDescription('');

      // Create a root node with two child nodes using the project name
      createRootNodeWithProjectName(newProjectName);

      // Explicitly set the current project to ensure it's selected
      console.log('Setting current project:', newProject);
      setCurrentProject(newProject);

      // Save the project with the root node and child nodes
      console.log('Saving project with root node and child nodes...');
      const saveSuccess = await saveProjectData(
        newProject.id,
        nodes,
        edges,
        newProjectName
      );
      console.log('Project saved successfully:', saveSuccess);

      // Show success message
      toast.current?.show({
        severity: 'success',
        summary: 'Success',
        detail: 'Project created successfully'
      });

      // Dispatch a custom event to center the view
      window.dispatchEvent(new CustomEvent('project-loaded'));

      // Force another layout update to ensure the graph is properly rendered
      setTimeout(() => {
        window.dispatchEvent(new CustomEvent('graph-layout-updated'));
      }, 100);
    } catch (error) {
      console.error('Error in project creation flow:', error);
      toast.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: error instanceof Error ? error.message : 'Failed to create project'
      });
    } finally {
      setLoading(false);
    }
  };

  // Create a root node with the project name as title and two child nodes
  const createRootNodeWithProjectName = (projectName: string) => {
    // Clear any existing nodes
    setNodes([]);
    setEdges([]);

    // Create a default richtext segment for the nodes
    const createDefaultSegment = () => ({
      id: Date.now() + Math.floor(Math.random() * 1000),
      type: 'richtext',
      content: ''
    });

    // Generate unique IDs for all nodes
    const rootNodeId = `node-${Date.now()}`;
    const childNode1Id = `node-${Date.now() + 1}`;
    const childNode2Id = `node-${Date.now() + 2}`;

    // Create a root node with the project name
    const rootNode = {
      id: rootNodeId,
      x: 0,
      y: 0,
      width: 336,
      height: 356,
      label: 'Root',
      title: projectName, // Use project name as the title
      content: '',
      color: '#4299e1',
      useCustomColor: true,  // Root node uses custom color by default
      inheritedColor: undefined,
      // Add segments to ensure the node is fully functional
      segments: [createDefaultSegment()],
      relationships: {
        parentId: undefined,
        childIds: [childNode1Id, childNode2Id] // Reference the child nodes
      }
    } as CanvasNode;

    // Create child node 1
    const childNode1 = {
      id: childNode1Id,
      x: -200, // Initial position, will be adjusted by layout
      y: 200,
      width: 336,
      height: 356,
      label: '',
      title: 'Node 1', // Default title for first child
      content: '',
      color: '#4299e1',
      useCustomColor: false,
      inheritedColor: '#4299e1',
      segments: [createDefaultSegment()],
      relationships: {
        parentId: rootNodeId, // Reference the root node
        childIds: []
      }
    } as CanvasNode;

    // Create child node 2
    const childNode2 = {
      id: childNode2Id,
      x: 200, // Initial position, will be adjusted by layout
      y: 200,
      width: 336,
      height: 356,
      label: '',
      title: 'Node 2', // Default title for second child
      content: '',
      color: '#4299e1',
      useCustomColor: false,
      inheritedColor: '#4299e1',
      segments: [createDefaultSegment()],
      relationships: {
        parentId: rootNodeId, // Reference the root node
        childIds: []
      }
    } as CanvasNode;

    console.log('Creating root node with ID:', rootNode.id, 'and two child nodes');

    // Create edges between root and child nodes
    const edge1 = {
      id: `edge-${rootNodeId}-${childNode1Id}`,
      source: rootNodeId,
      target: childNode1Id
    } as CanvasEdge;

    const edge2 = {
      id: `edge-${rootNodeId}-${childNode2Id}`,
      source: rootNodeId,
      target: childNode2Id
    } as CanvasEdge;

    // Apply layout to all nodes - force TOP_DOWN layout
    const allNodes = [rootNode, childNode1, childNode2];
    const layoutedNodes = calculateGraphLayout(allNodes, GraphLayout.TOP_DOWN);

    // Set nodes and edges synchronously
    setNodes(layoutedNodes);
    setEdges([edge1, edge2]);

    // Force a layout update event to ensure the graph is properly rendered
    setTimeout(() => {
      window.dispatchEvent(new CustomEvent('graph-layout-updated'));
    }, 100);
  };

  const confirmDeleteProject = () => {
    confirmDialog({
      id: 'project-delete-confirmation',
      message: 'Are you sure you want to delete this project?',
      header: 'Delete Confirmation',
      icon: 'pi pi-exclamation-triangle',
      acceptClassName: 'p-button-danger',
      accept: async () => {
        setLoading(true);
        const success = await deleteCurrentProject();
        setLoading(false);

        if (success) {
          toast.current?.show({
            severity: 'success',
            summary: 'Success',
            detail: 'Project deleted successfully'
          });
        } else {
          toast.current?.show({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to delete project'
          });
        }
      }
    });
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };

  if (!isAuthenticated) {
    return (
      <div className="tags-container">
        <div className="tags-header">
          <h3 className="tags-title">Project Manager</h3>
        </div>
        <div className="project-list-empty">
          <p>Sign in to manage projects</p>
        </div>
      </div>
    );
  }

  return (
    <div className="tags-container">
      <Toast ref={toast} />
      <ConfirmDialog key="project-delete-dialog" />

      <div className="tags-header">
        <h3 className="tags-title">Project Manager</h3>
        <div className="header-actions">
          <div className="user-tier-badge">{tierName}</div>
          <Button
            icon="pi pi-plus"
            className="p-button-rounded p-button-sm p-button-text"
            onClick={() => setShowNewProjectDialog(true)}
            disabled={loading || (isFree && projects.length >= getMaxProjectsAllowed(TierLevel.Free))}
            tooltip={isFree && projects.length >= getMaxProjectsAllowed(TierLevel.Free) ?
              "Free accounts are limited to 1 project. Upgrade to create more." : "Create new project"}
          />
        </div>
      </div>

      {projects.length === 0 ? (
        <div className="project-list-empty">
          <p>No projects yet</p>
          <Button
            label="Create New Project"
            icon="pi pi-plus"
            className="sidebar-new-project-button"
            onClick={() => setShowNewProjectDialog(true)}
            disabled={loading}
          />
        </div>
      ) : (
        <>
          <div className="project-list">
            {projects.map(project => (
              <div
                key={project.id}
                className={`project-item ${currentProject?.id === project.id ? 'active' : ''}`}
                onClick={() => handleProjectClick(project.id)}
              >
                <div className="project-item-content">
                  <div className="project-name">{project.name}</div>
                  <div className="project-date">Updated: {formatDate(project.updated_at)}</div>
                </div>
              </div>
            ))}
          </div>

          {/* Show upgrade message for free users */}
          {isFree && projects.length >= getMaxProjectsAllowed(TierLevel.Free) && (
            <div className="upgrade-message">
              <p>Free accounts are limited to {getMaxProjectsAllowed(TierLevel.Free)} project.</p>
              <Button
                label="Upgrade to Premium"
                className="p-button-sm p-button-outlined"
                icon="pi pi-arrow-up"
                onClick={() => {
                  // Show upgrade dialog or redirect to upgrade page
                  toast.current?.show({
                    severity: 'info',
                    summary: 'Upgrade',
                    detail: 'Contact administrator to upgrade your account.'
                  });
                }}
              />
            </div>
          )}
        </>
      )}

      {currentProject && (
        <div className="project-actions">
          <Button
            icon="pi pi-trash"
            className="p-button-rounded p-button-danger p-button-sm"
            onClick={confirmDeleteProject}
            disabled={loading}
            tooltip="Delete Project"
          />
        </div>
      )}

      <Dialog
        header="Create New Project"
        visible={showNewProjectDialog}
        onHide={() => setShowNewProjectDialog(false)}
        style={{ width: '450px' }}
        modal
        footer={(
          <div>
            <Button
              label="Cancel"
              icon="pi pi-times"
              className="p-button-text"
              onClick={() => setShowNewProjectDialog(false)}
              disabled={loading}
            />
            <Button
              label="Create"
              icon="pi pi-check"
              onClick={handleCreateProject}
              loading={loading}
            />
          </div>
        )}
      >
        <div className="p-field">
          <label htmlFor="projectName">Project Name</label>
          <InputText
            id="projectName"
            value={newProjectName}
            onChange={(e) => setNewProjectName(e.target.value)}
            required
            className="p-inputtext-lg p-d-block"
            style={{ width: '100%' }}
          />
        </div>
        <div className="p-field">
          <label htmlFor="projectDescription">Description (Optional)</label>
          <InputTextarea
            id="projectDescription"
            value={newProjectDescription}
            onChange={(e) => setNewProjectDescription(e.target.value)}
            rows={3}
            className="p-d-block"
            style={{ width: '100%' }}
          />
        </div>
      </Dialog>
    </div>
  );
};

export default ProjectList;

// Export a function to show the new project dialog from anywhere
export const showNewProjectDialog = () => {
  if (globalShowNewProjectDialog) {
    globalShowNewProjectDialog();
  } else {
    console.error('New project dialog function not available');
  }
};
