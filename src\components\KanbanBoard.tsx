/**
 * KanbanBoard - Component for displaying nodes in a Kanban board layout
 *
 * This component displays nodes in columns based on tags or completion status.
 */

import React, { useState, useEffect } from 'react';
import { NodeModel } from '../model/NodeModel';
import { nodeStore } from '../model/NodeStore';
import { nodeController } from '../controller/NodeController';
import { useTags } from '../context/TagContext';
import { Tag } from '../types';
import { filterController, FilterType } from '../controller/FilterController';
import './KanbanBoard.css';

interface KanbanBoardProps {
  onNodeClick?: (nodeId: string) => void;
  selectedTags?: string[];
  searchQuery?: string;
  searchResults?: Array<{id: string, type: string, title: string, description: string, matchType: string}>;
}

interface KanbanColumn {
  id: string;
  title: string;
  color: string;
  nodes: NodeModel[];
}

export const KanbanBoard: React.FC<KanbanBoardProps> = ({
  onNodeClick,
  selectedTags = [],
  searchQuery = '',
  searchResults = []
}) => {
  const { tags } = useTags();
  const [columns, setColumns] = useState<KanbanColumn[]>([]);
  const [allNodes, setAllNodes] = useState<NodeModel[]>([]);
  const [groupBy, setGroupBy] = useState<'tags' | 'completion'>('tags');
  const [highlightedNodeId, setHighlightedNodeId] = useState<string | null>(null);

  // Load nodes
  useEffect(() => {
    // Get all nodes
    const nodes = nodeStore.getAllNodes();
    setAllNodes(nodes);

    // Subscribe to node changes
    const handleNodesChanged = () => {
      setAllNodes(nodeStore.getAllNodes());
    };

    nodeStore.on('graph:updated', handleNodesChanged);

    return () => {
      nodeStore.off('graph:updated', handleNodesChanged);
    };
  }, []);

  // Create columns based on groupBy
  useEffect(() => {
    if (groupBy === 'tags') {
      // If selectedTags are provided, only show columns for selected tags
      // If no tags are selected, show all tags (default behavior)
      const tagsToShow = selectedTags.length > 0
        ? tags.filter(tag => selectedTags.includes(tag.id))
        : tags;

      // Create a column for each tag
      const tagColumns: KanbanColumn[] = tagsToShow.map(tag => ({
        id: tag.id,
        title: tag.name,
        color: tag.color,
        nodes: allNodes.filter(node => node.tags.includes(tag.id))
      }));

      // Always add a column for untagged nodes
      const untaggedNodes = allNodes.filter(node => node.tags.length === 0);
      if (untaggedNodes.length > 0) {
        tagColumns.push({
          id: 'untagged',
          title: 'Untagged',
          color: '#a0aec0',
          nodes: untaggedNodes
        });
      }

      setColumns(tagColumns);
    } else {
      // Group by completion status
      const completedNodes = allNodes.filter(node => node.completed);
      const incompleteNodes = allNodes.filter(node => !node.completed);

      setColumns([
        {
          id: 'incomplete',
          title: 'To Do',
          color: '#4299e1',
          nodes: incompleteNodes
        },
        {
          id: 'completed',
          title: 'Completed',
          color: '#48bb78',
          nodes: completedNodes
        }
      ]);
    }
  }, [allNodes, tags, groupBy, selectedTags]);

  // Handle search results - scroll to and highlight first result
  useEffect(() => {
    if (searchResults.length > 0) {
      const firstResult = searchResults[0];
      setHighlightedNodeId(firstResult.id);

      // Scroll to the card
      setTimeout(() => {
        const cardElement = document.querySelector(`[data-node-id="${firstResult.id}"]`);
        if (cardElement) {
          cardElement.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          });
        }
      }, 100);
    } else {
      setHighlightedNodeId(null);
    }
  }, [searchResults]);

  // Handle node click
  const handleNodeClick = (nodeId: string) => {
    if (onNodeClick) {
      onNodeClick(nodeId);
    }
  };

  // Handle drag start
  const handleDragStart = (e: React.DragEvent, nodeId: string) => {
    e.dataTransfer.setData('nodeId', nodeId);
  };

  // Handle drag over
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  // Handle drop
  const handleDrop = (e: React.DragEvent, columnId: string) => {
    e.preventDefault();

    const nodeId = e.dataTransfer.getData('nodeId');

    if (groupBy === 'tags') {
      // Handle tag-based grouping
      if (columnId === 'untagged') {
        // Remove all tags from the node
        nodeController.updateNode(nodeId, { tags: [] });
      } else {
        // Replace all tags with just the column tag
        nodeController.updateNode(nodeId, { tags: [columnId] });
      }
    } else {
      // Handle completion-based grouping
      const completed = columnId === 'completed';
      nodeController.updateNode(nodeId, { completed });
    }
  };

  // Toggle grouping
  const toggleGroupBy = () => {
    setGroupBy(prev => prev === 'tags' ? 'completion' : 'tags');
  };

  return (
    <div className="kanban-board">
      <div className="kanban-header">
        <h2>Kanban Board</h2>
        <button
          className="toggle-group-button"
          onClick={toggleGroupBy}
        >
          Group by: {groupBy === 'tags' ? 'Tags' : 'Completion'}
        </button>
      </div>

      <div className="kanban-columns">
        {columns.map(column => (
          <div
            key={column.id}
            className="kanban-column"
            onDragOver={handleDragOver}
            onDrop={(e) => handleDrop(e, column.id)}
          >
            <div
              className="column-header"
              style={{ backgroundColor: column.color }}
            >
              <h3>{column.title}</h3>
              <span className="node-count">{column.nodes.length}</span>
            </div>

            <div className="column-content">
              {column.nodes.map(node => (
                <div
                  key={node.id}
                  className={`kanban-card ${highlightedNodeId === node.id ? 'search-highlighted' : ''}`}
                  data-node-id={node.id}
                  onClick={() => handleNodeClick(node.id)}
                  draggable
                  onDragStart={(e) => handleDragStart(e, node.id)}
                  style={{
                    borderLeftColor: node.color,
                    opacity: node.completed ? 0.7 : 1,
                    boxShadow: highlightedNodeId === node.id
                      ? '0 0 0 3px #4299e1, 0 4px 12px rgba(66, 153, 225, 0.4)'
                      : undefined
                  }}
                >
                  <div className="card-title">
                    {node.title || 'Untitled'}
                    {node.completed && <span className="completed-mark">✓</span>}
                  </div>

                  <div className="card-content">
                    {(() => {
                      // Get content from node content or segments
                      let displayContent = '';

                      if (node.content && node.content.trim()) {
                        displayContent = node.content;
                      } else if (node.segments && node.segments.length > 0) {
                        // Extract content from segments
                        const segmentContent = node.segments
                          .filter(segment => segment.content && segment.content.trim())
                          .map(segment => segment.content)
                          .join(' ');
                        displayContent = segmentContent;
                      }

                      if (!displayContent.trim()) {
                        displayContent = 'No content';
                      }

                      // Truncate if too long
                      return displayContent.length > 150
                        ? `${displayContent.substring(0, 150)}...`
                        : displayContent;
                    })()}
                  </div>

                  {node.tags.length > 0 && (
                    <div className="card-tags">
                      {node.tags.map(tagId => {
                        const tag = tags.find(t => t.id === tagId);
                        return tag ? (
                          <span
                            key={tagId}
                            className="card-tag"
                            style={{ backgroundColor: tag.color }}
                          >
                            {tag.name}
                          </span>
                        ) : null;
                      })}
                    </div>
                  )}
                </div>
              ))}

              {column.nodes.length === 0 && (
                <div className="empty-column">
                  No nodes in this column
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
