/**
 * SimpleLayout - A simplified layout component for MVC components
 *
 * This is a simplified version of the Layout component that doesn't require
 * all the props of the original Layout component.
 */

import React from 'react';
import './Layout.css';

interface SimpleLayoutProps {
  children: React.ReactNode;
}

const SimpleLayout: React.FC<SimpleLayoutProps> = ({ children }) => {
  return (
    <div className="layout-container">
      <main className="main-content" style={{ position: 'relative', zIndex: 1, height: '100vh', overflow: 'hidden' }}>
        {children}
      </main>
    </div>
  );
};

export default SimpleLayout;
