import React, { useState, useRef } from 'react';
import { Dialog } from 'primereact/dialog';
import { But<PERSON> } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { Toast } from 'primereact/toast';
import { useProject } from '../../context/ProjectContext';
import { useAuthWrapper } from '../../context/AuthWrapper';

interface NewProjectModalProps {
  visible: boolean;
  onHide: () => void;
  onProjectCreated?: (projectId: string) => void;
}

const NewProjectModal: React.FC<NewProjectModalProps> = ({
  visible,
  onHide,
  onProjectCreated
}) => {
  const { createNewProject } = useProject();
  const { isAuthenticated } = useAuthWrapper();
  const [projectName, setProjectName] = useState('');
  const [projectDescription, setProjectDescription] = useState('');
  const [loading, setLoading] = useState(false);
  const toast = useRef<Toast>(null);

  const handleCreateProject = async () => {
    if (!projectName.trim()) {
      toast.current?.show({
        severity: 'warn',
        summary: 'Warning',
        detail: 'Project name is required'
      });
      return;
    }

    setLoading(true);
    try {
      const newProject = await createNewProject(projectName, projectDescription);
      
      if (newProject) {
        toast.current?.show({
          severity: 'success',
          summary: 'Success',
          detail: 'Project created successfully'
        });

        // Reset form
        setProjectName('');
        setProjectDescription('');
        
        // Call callback if provided
        if (onProjectCreated) {
          onProjectCreated(newProject.id);
        }
        
        // Close modal
        onHide();
      } else {
        throw new Error('Failed to create project');
      }
    } catch (error) {
      console.error('Error creating project:', error);
      toast.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: error instanceof Error ? error.message : 'Failed to create project'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setProjectName('');
    setProjectDescription('');
    onHide();
  };

  return (
    <>
      <Toast ref={toast} />
      <Dialog
        header="Create New Project"
        visible={visible}
        onHide={handleCancel}
        style={{ width: '450px' }}
        modal
        footer={(
          <div>
            <Button
              label="Cancel"
              icon="pi pi-times"
              className="p-button-text"
              onClick={handleCancel}
              disabled={loading}
            />
            <Button
              label="Create"
              icon="pi pi-check"
              onClick={handleCreateProject}
              loading={loading}
            />
          </div>
        )}
      >
        <div className="p-fluid">
          <div className="p-field">
            <label htmlFor="name">Project Name</label>
            <InputText
              id="name"
              value={projectName}
              onChange={(e) => setProjectName(e.target.value)}
              required
              autoFocus
              placeholder="Enter project name"
            />
          </div>
          <div className="p-field" style={{ marginTop: '1rem' }}>
            <label htmlFor="description">Description (Optional)</label>
            <InputText
              id="description"
              value={projectDescription}
              onChange={(e) => setProjectDescription(e.target.value)}
              placeholder="Enter project description"
            />
          </div>
        </div>
      </Dialog>
    </>
  );
};

export default NewProjectModal;
