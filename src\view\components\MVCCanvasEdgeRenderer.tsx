import React, { useRef, useEffect, useState } from 'react';
import { NodeStore } from '../../model/NodeStore';
import { NodeModel } from '../../model/NodeModel';
import { transformController } from '../../controller/TransformController';

interface MVCCanvasEdgeRendererProps {
  nodeStore: NodeStore;
  style?: React.CSSProperties;
  debug?: boolean;
}

// Edge types (matching current implementation)
enum EdgeType {
  DEFAULT = 'default',
  PARENT_CHILD = 'parent-child',
  BIDIRECTIONAL = 'bidirectional',
  REFERENCE = 'reference'
}

// Edge styles by type
const EDGE_STYLES = {
  [EdgeType.DEFAULT]: { color: '#94a3b8', width: 2, dash: [] },
  [EdgeType.PARENT_CHILD]: { color: '#60a5fa', width: 2.5, dash: [] },
  [EdgeType.BIDIRECTIONAL]: { color: '#f97316', width: 2, dash: [6, 3] },
  [EdgeType.REFERENCE]: { color: '#8b5cf6', width: 1.5, dash: [2, 2] }
};

export const MVCCanvasEdgeRenderer: React.FC<MVCCanvasEdgeRendererProps> = ({
  nodeStore,
  style,
  debug = false
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [nodes, setNodes] = useState<NodeModel[]>([]);
  const animationFrameRef = useRef<number | null>(null);
  const isRenderingRef = useRef(false);
  const frameCountRef = useRef(0);
  const lastRenderTimeRef = useRef(0);

  // Subscribe to node store updates
  useEffect(() => {
    console.log('MVCCanvasEdgeRenderer: Setting up node store subscription');

    // Initial load of nodes
    const initialNodes = nodeStore.getAllNodes();
    console.log(`MVCCanvasEdgeRenderer: Initial load of ${initialNodes.length} nodes`);

    // Log node relationships for debugging
    if (debug) {
      initialNodes.forEach(node => {
        console.log(`Node ${node.id}: parentId=${node.relationships.parentId}, childIds=[${node.relationships.childIds.join(', ')}]`);
      });
    }

    setNodes(initialNodes);

    // Subscribe to changes
    const handleNodesUpdated = () => {
      console.log('MVCCanvasEdgeRenderer: Graph updated, refreshing nodes');
      setNodes(nodeStore.getAllNodes());
    };

    // Subscribe to all relevant events
    const unsubscribeGraphUpdated = nodeStore.on('graph:updated', handleNodesUpdated);
    const unsubscribeNodeAdded = nodeStore.on('node:added', handleNodesUpdated);
    const unsubscribeNodeUpdated = nodeStore.on('node:updated', handleNodesUpdated);
    const unsubscribeNodeRemoved = nodeStore.on('node:removed', handleNodesUpdated);

    return () => {
      console.log('MVCCanvasEdgeRenderer: Cleaning up node store subscription');
      unsubscribeGraphUpdated();
      unsubscribeNodeAdded();
      unsubscribeNodeUpdated();
      unsubscribeNodeRemoved();
    };
  }, [nodeStore, debug]);

  // Set up canvas and rendering
  useEffect(() => {
    console.log('MVCCanvasEdgeRenderer: Setting up canvas rendering');

    const canvas = canvasRef.current;
    if (!canvas) {
      console.warn('MVCCanvasEdgeRenderer: Canvas ref is null');
      return;
    }

    // Get canvas context
    const ctx = canvas.getContext('2d');
    if (!ctx) {
      console.warn('MVCCanvasEdgeRenderer: Could not get 2D context');
      return;
    }

    // Set up canvas size with pixel ratio handling
    const resizeCanvas = () => {
      const dpr = window.devicePixelRatio || 1;

      // Get the container size
      canvas.style.width = '100%';
      canvas.style.height = '100%';

      const rect = canvas.getBoundingClientRect();

      console.log(`MVCCanvasEdgeRenderer: Resizing canvas to ${rect.width}x${rect.height} (DPR: ${dpr})`);

      // Set display size (css pixels)
      canvas.style.width = `${rect.width}px`;
      canvas.style.height = `${rect.height}px`;

      // Set actual size in memory (scaled for retina)
      canvas.width = Math.floor(rect.width * dpr);
      canvas.height = Math.floor(rect.height * dpr);

      // Scale all drawing operations by the dpr
      ctx.scale(dpr, dpr);

      // Verify canvas size
      console.log(`MVCCanvasEdgeRenderer: Canvas size set to ${canvas.width}x${canvas.height} (${canvas.width/dpr}x${canvas.height/dpr} CSS px)`);

      // Force a render after resize
      setTimeout(() => {
        console.log('MVCCanvasEdgeRenderer: Forcing render after resize');
        requestRender();
      }, 100);
    };

    // Helper function to round to device pixels
    const roundToPixel = (value: number): number => {
      const dpr = window.devicePixelRatio || 1;
      return Math.round(value * dpr) / dpr;
    };

    // Request a render (debounced)
    const requestRender = () => {
      if (!isRenderingRef.current) {
        isRenderingRef.current = true;
        animationFrameRef.current = requestAnimationFrame(() => {
          const now = performance.now();
          renderEdges();

          // Calculate FPS for debugging
          if (debug) {
            frameCountRef.current++;
            const elapsed = now - lastRenderTimeRef.current;
            if (elapsed > 1000) {
              const fps = Math.round((frameCountRef.current * 1000) / elapsed);
              console.log(`MVCCanvasEdgeRenderer: Rendering at ${fps} FPS`);
              frameCountRef.current = 0;
              lastRenderTimeRef.current = now;
            }
          }

          isRenderingRef.current = false;
        });
      }
    };

    // Function to render all edges
    const renderEdges = () => {
      if (!ctx || !canvas) {
        console.error('MVCCanvasEdgeRenderer: Canvas or context is null, cannot render edges');
        return;
      }

      console.log(`MVCCanvasEdgeRenderer: renderEdges called, debug=${debug}`);
      console.log(`MVCCanvasEdgeRenderer: Canvas dimensions: ${canvas.width}x${canvas.height}`);

      if (debug) {
        console.log(`MVCCanvasEdgeRenderer: Debug mode is ON`);
      }

      // Clear canvas
      ctx.clearRect(0, 0, canvas.width / (window.devicePixelRatio || 1),
                          canvas.height / (window.devicePixelRatio || 1));

      // Get current transform - CRITICAL for alignment with DOM
      const transform = transformController.getTransform();

      console.log(`MVCCanvasEdgeRenderer: Current transform: scale=${transform.scale.toFixed(2)}, offset=(${transform.offset.x.toFixed(1)}, ${transform.offset.y.toFixed(1)})`);

      // Apply transform to canvas context - MUST match DOM transform exactly
      ctx.save();
      ctx.translate(
        roundToPixel(transform.offset.x),
        roundToPixel(transform.offset.y)
      );
      ctx.scale(transform.scale, transform.scale);

      console.log(`MVCCanvasEdgeRenderer: Applied transform to canvas context`);

      // Debug visualization elements have been disabled
      // Test line, center cross, and viewport rectangle are disabled for production

      // Reset stroke style for edge rendering
      ctx.strokeStyle = 'black';
      ctx.lineWidth = 1;

      console.log('MVCCanvasEdgeRenderer: Drew test lines and shapes');

      // Create a map for quick node lookup
      const nodeMap = new Map<string, NodeModel>();
      nodes.forEach(node => nodeMap.set(node.id, node));

      if (debug) {
        console.log(`MVCCanvasEdgeRenderer: Rendering with ${nodes.length} nodes`);
        nodes.forEach(node => {
          console.log(`Node ${node.id}: parentId=${node.relationships.parentId}, childIds=[${node.relationships.childIds.join(', ')}]`);
        });
      }

      // Collect all edges
      const edges: Array<{
        id: string;
        source: string;
        target: string;
        type: EdgeType;
      }> = [];

      // Extract edges from node relationships
      console.log(`MVCCanvasEdgeRenderer: Extracting edges from ${nodes.length} nodes`);

      // Log all nodes and their relationships
      if (nodes.length === 0) {
        console.warn('MVCCanvasEdgeRenderer: No nodes to render edges for!');
      } else {
        console.log('MVCCanvasEdgeRenderer: Nodes and their relationships:');
        nodes.forEach((node: NodeModel) => {
          console.log(`Node ${node.id}: parentId=${node.relationships.parentId}, childIds=[${node.relationships.childIds.join(', ')}], position=(${node.position.x}, ${node.position.y})`);
        });
      }

      nodes.forEach((node: NodeModel) => {
        const relationships = node.relationships;

        // Add parent-child edges
        if (relationships.parentId) {
          console.log(`MVCCanvasEdgeRenderer: Node ${node.id} has parent ${relationships.parentId}`);

          if (nodeMap.has(relationships.parentId)) {
            const parentNode = nodeMap.get(relationships.parentId);

            console.log(`MVCCanvasEdgeRenderer: Found parent-child relationship: ${relationships.parentId} -> ${node.id}`);
            console.log(`  Parent node: ${parentNode?.id}, position: (${parentNode?.position.x}, ${parentNode?.position.y})`);
            console.log(`  Child node: ${node.id}, position: (${node.position.x}, ${node.position.y})`);

            edges.push({
              id: `${relationships.parentId}-${node.id}`,
              source: relationships.parentId,
              target: node.id,
              type: EdgeType.PARENT_CHILD
            });
          } else {
            console.warn(`MVCCanvasEdgeRenderer: Parent node ${relationships.parentId} not found for node ${node.id}`);
          }
        } else {
          console.log(`MVCCanvasEdgeRenderer: Node ${node.id} has no parent`);
        }
      });

      if (debug) {
        console.log(`MVCCanvasEdgeRenderer: Found ${edges.length} edges to render`);
      }

      // Group edges by type for batch rendering
      const edgesByType = new Map<EdgeType, Array<{
        source: NodeModel;
        target: NodeModel;
      }>>();

      // Initialize edge type groups
      Object.values(EdgeType).forEach(type => {
        edgesByType.set(type as EdgeType, []);
      });

      // Group edges by type
      edges.forEach(edge => {
        const sourceNode = nodeMap.get(edge.source);
        const targetNode = nodeMap.get(edge.target);

        if (sourceNode && targetNode) {
          // Get the current group for this edge type
          const typeGroup = edgesByType.get(edge.type);

          if (typeGroup) {
            // Add the edge to the group
            typeGroup.push({ source: sourceNode, target: targetNode });

            // Update the map with the modified group
            edgesByType.set(edge.type, typeGroup);

            if (debug) {
              console.log(`MVCCanvasEdgeRenderer: Added edge from ${sourceNode.id} to ${targetNode.id} of type ${edge.type}`);
            }
          }
        }
      });

      if (debug) {
        // Log the contents of each edge type group
        edgesByType.forEach((group, type) => {
          console.log(`MVCCanvasEdgeRenderer: Type ${type} has ${group.length} edges`);
        });
      }

      // Render edges by type (to minimize context switching)
      console.log(`MVCCanvasEdgeRenderer: About to render edges by type`);

      // Draw direct lines between all nodes for debugging
      ctx.strokeStyle = 'purple';
      ctx.lineWidth = 3;
      ctx.setLineDash([5, 5]);

      console.log(`MVCCanvasEdgeRenderer: Drawing direct lines between all nodes for debugging`);

      // Draw direct lines between all parent-child pairs
      nodes.forEach(node => {
        if (node.relationships.parentId) {
          const parentNode = nodeMap.get(node.relationships.parentId);
          if (parentNode) {
            console.log(`MVCCanvasEdgeRenderer: Drawing direct line from ${parentNode.id} to ${node.id}`);

            // Draw a direct line
            ctx.beginPath();
            ctx.moveTo(
              parentNode.position.x + parentNode.dimensions.width / 2,
              parentNode.position.y + parentNode.dimensions.height
            );
            ctx.lineTo(
              node.position.x + node.dimensions.width / 2,
              node.position.y
            );
            ctx.stroke();
          }
        }
      });

      // Reset line dash
      ctx.setLineDash([]);

      edgesByType.forEach((edgeGroup, type) => {
        if (edgeGroup.length === 0) {
          console.log(`MVCCanvasEdgeRenderer: No edges of type ${type} to render`);
          return;
        }

        console.log(`MVCCanvasEdgeRenderer: Rendering ${edgeGroup.length} edges of type ${type}`);

        // Set style for this edge type
        const style = EDGE_STYLES[type];
        ctx.strokeStyle = style.color;
        ctx.lineWidth = style.width;

        // Set line dash if needed
        if (style.dash.length > 0) {
          ctx.setLineDash(style.dash);
        } else {
          ctx.setLineDash([]);
        }

        // Draw all edges of this type
        edgeGroup.forEach(({ source, target }) => {
          try {
            console.log(`MVCCanvasEdgeRenderer: Drawing edge from ${source.id} to ${target.id}`);
            drawEdge(ctx, source, target, debug);
          } catch (error) {
            console.error(`MVCCanvasEdgeRenderer: Error drawing edge from ${source.id} to ${target.id}:`, error);
          }
        });
      });

      // Restore canvas context
      ctx.restore();

      // Draw debug overlay if enabled
      if (debug) {
        drawDebugOverlay(ctx, transform, edges.length);
      }
    };

    // Function to draw a single edge
    const drawEdge = (
      ctx: CanvasRenderingContext2D,
      sourceNode: NodeModel,
      targetNode: NodeModel,
      debug: boolean
    ) => {
      if (debug) {
        console.log(`MVCCanvasEdgeRenderer: Drawing edge from ${sourceNode.id} to ${targetNode.id}`);
        console.log(`  Source position: (${sourceNode.position.x}, ${sourceNode.position.y}), dimensions: ${sourceNode.dimensions.width}x${sourceNode.dimensions.height}`);
        console.log(`  Target position: (${targetNode.position.x}, ${targetNode.position.y}), dimensions: ${targetNode.dimensions.width}x${targetNode.dimensions.height}`);
      }

      // CRITICAL: Use the exact same position calculation as DOM nodes
      // Since we're using DOM transform on the canvas element, we don't need to apply any transform here

      // Source point (bottom center of parent node)
      const sourceX = roundToPixel(sourceNode.position.x + sourceNode.dimensions.width / 2);
      const sourceY = roundToPixel(sourceNode.position.y + sourceNode.dimensions.height);

      // Target point (top center of child node)
      const targetX = roundToPixel(targetNode.position.x + targetNode.dimensions.width / 2);
      const targetY = roundToPixel(targetNode.position.y);

      if (debug) {
        console.log(`  Connection points: source=(${sourceX}, ${sourceY}), target=(${targetX}, ${targetY})`);
      }

      // Calculate control points for S-curve
      const dx = targetX - sourceX;
      const dy = targetY - sourceY;
      const distance = Math.sqrt(dx * dx + dy * dy);

      // Calculate vertical offsets for the control points
      let verticalOffset;
      const isVerticallyAligned = Math.abs(dx) < 10;

      if (isVerticallyAligned) {
        // Special case for vertically aligned nodes
        verticalOffset = Math.max(30, distance * 0.15);
      } else if (distance < 150) {
        // For very close nodes, use a fixed minimum offset
        verticalOffset = Math.max(50, distance * 0.5);
      } else if (distance < 400) {
        // For medium distances, scale the offset with distance
        verticalOffset = distance * 0.4;
      } else {
        // For long distances, cap the maximum offset
        verticalOffset = Math.min(200, distance * 0.3);
      }

      // Calculate control points
      let cp1x, cp1y, cp2x, cp2y;

      if (isVerticallyAligned) {
        // For vertical alignment, add a slight horizontal offset
        const horizontalOffset = 30;
        cp1x = roundToPixel(sourceX + horizontalOffset);
        cp1y = roundToPixel(sourceY + verticalOffset);
        cp2x = roundToPixel(targetX - horizontalOffset);
        cp2y = roundToPixel(targetY - verticalOffset);
      } else {
        // Standard case - straight vertical control points
        cp1x = roundToPixel(sourceX);
        cp1y = roundToPixel(sourceY + verticalOffset);
        cp2x = roundToPixel(targetX);
        cp2y = roundToPixel(targetY - verticalOffset);
      }

      // Draw the path
      console.log(`MVCCanvasEdgeRenderer: Drawing path from (${sourceX}, ${sourceY}) to (${targetX}, ${targetY})`);
      console.log(`  Control points: (${cp1x}, ${cp1y}) and (${cp2x}, ${cp2y})`);

      ctx.beginPath();
      ctx.moveTo(sourceX, sourceY);
      ctx.bezierCurveTo(cp1x, cp1y, cp2x, cp2y, targetX, targetY);
      ctx.stroke();

      console.log(`MVCCanvasEdgeRenderer: Path drawn with stroke style: ${ctx.strokeStyle}, line width: ${ctx.lineWidth}`);

      // Draw a simple line for debugging
      ctx.beginPath();
      ctx.moveTo(sourceX, sourceY);
      ctx.lineTo(targetX, targetY);
      ctx.stroke();

      // Draw arrowhead
      drawArrowhead(ctx, targetX, targetY, cp2x, cp2y);

      // Debug visualization to verify alignment
      if (debug) {
        // Draw connection points as circles
        ctx.fillStyle = 'red';
        ctx.beginPath();
        ctx.arc(sourceX, sourceY, 4, 0, Math.PI * 2);
        ctx.fill();

        ctx.fillStyle = 'blue';
        ctx.beginPath();
        ctx.arc(targetX, targetY, 4, 0, Math.PI * 2);
        ctx.fill();

        // Draw control points
        ctx.fillStyle = 'green';
        ctx.beginPath();
        ctx.arc(cp1x, cp1y, 3, 0, Math.PI * 2);
        ctx.fill();

        ctx.beginPath();
        ctx.arc(cp2x, cp2y, 3, 0, Math.PI * 2);
        ctx.fill();
      }
    };

    // Function to draw arrowhead
    const drawArrowhead = (
      ctx: CanvasRenderingContext2D,
      x: number,
      y: number,
      fromX: number,
      fromY: number
    ) => {
      const headLength = 10;
      const angle = Math.atan2(y - fromY, x - fromX);

      // Round coordinates for pixel-perfect rendering
      const p1x = roundToPixel(x - headLength * Math.cos(angle - Math.PI / 6));
      const p1y = roundToPixel(y - headLength * Math.sin(angle - Math.PI / 6));
      const p2x = roundToPixel(x - headLength * Math.cos(angle + Math.PI / 6));
      const p2y = roundToPixel(y - headLength * Math.sin(angle + Math.PI / 6));

      ctx.beginPath();
      ctx.moveTo(x, y);
      ctx.lineTo(p1x, p1y);
      ctx.lineTo(p2x, p2y);
      ctx.closePath();
      ctx.fillStyle = ctx.strokeStyle;
      ctx.fill();
    };

    // Draw debug overlay with transform info
    const drawDebugOverlay = (
      ctx: CanvasRenderingContext2D,
      transform: { scale: number, offset: { x: number, y: number } },
      edgeCount: number
    ) => {
      const dpr = window.devicePixelRatio || 1;

      // Since we're using DOM transform, we need to adjust the overlay position
      // to account for the transform
      const overlayX = 10 / transform.scale - transform.offset.x / transform.scale;
      const overlayY = 10 / transform.scale - transform.offset.y / transform.scale;

      ctx.save();

      ctx.font = `${12 / transform.scale}px monospace`;
      ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
      ctx.fillRect(overlayX, overlayY, 300 / transform.scale, 100 / transform.scale);
      ctx.fillStyle = 'white';

      const lineHeight = 20 / transform.scale;
      ctx.fillText(`Transform: scale=${transform.scale.toFixed(2)}, offset=(${transform.offset.x.toFixed(1)}, ${transform.offset.y.toFixed(1)})`,
                  overlayX + 10 / transform.scale, overlayY + lineHeight);
      ctx.fillText(`Canvas size: ${canvas.width}x${canvas.height} (${(canvas.width/dpr)}x${(canvas.height/dpr)} CSS px)`,
                  overlayX + 10 / transform.scale, overlayY + lineHeight * 2);
      ctx.fillText(`Device pixel ratio: ${dpr}`,
                  overlayX + 10 / transform.scale, overlayY + lineHeight * 3);
      ctx.fillText(`Rendering ${edgeCount} edges`,
                  overlayX + 10 / transform.scale, overlayY + lineHeight * 4);

      ctx.restore();
    };

    // Initial size setup
    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // Set up transform change listener
    const handleTransformChange = () => {
      requestRender();
    };

    // Set up node drag listener for real-time edge updates
    const handleNodeDrag = (nodeId: string) => {
      // Remove debug logging during drag for performance
      requestRender();
    };

    // Set up node position update listener
    const handleNodePositionUpdated = (nodeId: string) => {
      // Remove debug logging during position updates for performance
      requestRender();
    };

    // Subscribe to events
    transformController.on('transform:updated', handleTransformChange);
    nodeStore.on('node:dragging', handleNodeDrag);
    nodeStore.on('node:position:updated', handleNodePositionUpdated);

    // Log event registration
    if (debug) {
      console.log('MVCCanvasEdgeRenderer: Registered event handlers for transform:updated, node:dragging, and node:position:updated');
    }

    // Initial render
    console.log('MVCCanvasEdgeRenderer: Requesting initial render');
    requestRender();

    // Force a render after a short delay to ensure everything is initialized
    setTimeout(() => {
      console.log('MVCCanvasEdgeRenderer: Forcing render after initialization');
      requestRender();
    }, 500);

    // Clean up
    return () => {
      console.log('MVCCanvasEdgeRenderer: Cleaning up resources');
      window.removeEventListener('resize', resizeCanvas);
      transformController.off('transform:updated', handleTransformChange);
      nodeStore.off('node:dragging', handleNodeDrag);
      nodeStore.off('node:position:updated', handleNodePositionUpdated);

      if (animationFrameRef.current !== null) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
    };
  }, [nodes, debug]);

  return (
    <canvas
      ref={canvasRef}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        zIndex: 1, // Must be below nodes but above background
        pointerEvents: 'none',
        border: 'none', // Debug border disabled for production
        ...style
      }}
    />
  );
};

export default MVCCanvasEdgeRenderer;
