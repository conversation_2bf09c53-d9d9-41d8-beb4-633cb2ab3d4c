/**
 * NodeTags - Component for displaying tags on nodes
 *
 * This component displays tags associated with a node.
 */

import React, { useState, useEffect } from 'react';
import { useTags } from '../context/TagContext';
import { Tag } from '../types';
import './NodeTags.css';

interface NodeTagsProps {
  tagIds: string[];
  onTagClick?: (tagId: string) => void;
  maxVisible?: number;
}

export const NodeTags: React.FC<NodeTagsProps> = ({
  tagIds,
  onTagClick,
  maxVisible = 3
}) => {
  const { getNodeTags } = useTags();
  const [showAll, setShowAll] = useState(false);

  // Get tags for this node
  const tags = getNodeTags(tagIds);

  // No tags to display
  if (tags.length === 0) {
    return null;
  }

  // Handle tag click
  const handleTagClick = (tagId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    if (onTagClick) {
      onTagClick(tagId);
    }
  };

  // Toggle show all
  const toggleShowAll = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowAll(!showAll);
  };

  // Determine which tags to display
  const visibleTags = showAll ? tags : tags.slice(0, maxVisible);
  const hasMore = tags.length > maxVisible;

  return (
    <div className="node-tags">
      {visibleTags.map(tag => (
        <div
          key={tag.id}
          className="node-tag"
          style={{ backgroundColor: tag.color }}
          onClick={(e) => handleTagClick(tag.id, e)}
          title={tag.name}
        >
          {tag.name}
        </div>
      ))}

      {hasMore && !showAll && (
        <div
          className="node-tag more-tag"
          onClick={toggleShowAll}
        >
          +{tags.length - maxVisible} more
        </div>
      )}

      {showAll && hasMore && (
        <div
          className="node-tag less-tag"
          onClick={toggleShowAll}
        >
          Show less
        </div>
      )}
    </div>
  );
};
