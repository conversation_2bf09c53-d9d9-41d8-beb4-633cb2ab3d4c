﻿/* src/components/Graph/CustomNode.css */

/* Base Node Container & Scaling Transitions */
.custom-node-container {
    position: relative;
    transition: height 0.2s ease, width 0.2s ease;
    will-change: transform, opacity;
    cursor: pointer !important; /* Force consistent cursor */
}

.custom-node {
    position: absolute;
    pointer-events: auto; /* Allow node interactions */
    background: var(--node-bg);
    border: 2px solid var(--node-border);
    border-radius: 8px;
    padding: 0;
    display: flex;
    flex-direction: column;
    box-shadow: 0 1px 3px var(--shadow-color), 0 1px 2px var(--shadow-color);
    transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1), transform 0.6s cubic-bezier(0.22, 1, 0.36, 1), opacity 0.3s ease, background-color var(--transition-speed), border-color var(--transition-speed);
    cursor: default;
    user-select: none;
    -webkit-user-select: none;
    overflow: visible; /* Changed from hidden to visible */
    will-change: transform, width, height;
    position: relative; /* Ensure pseudo-element positioning works */
    color: var(--node-text);
}

/* Special styling for root node to indicate it cannot be moved */
.custom-node.root-node {
    border: 3px solid var(--accent-color);
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.3), 0 4px 8px var(--shadow-color);
    background-color: var(--bg-secondary);
}

/* Add a small indicator to show the root node cannot be moved */
.custom-node.root-node::after {
    content: '🔒';
    position: absolute;
    top: -10px;
    right: -10px;
    background: var(--accent-color);
    color: var(--button-primary-text);
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    box-shadow: 0 2px 4px var(--shadow-color);
    z-index: 10;
    transition: background-color var(--transition-speed), color var(--transition-speed);
}

.custom-node::before {
    content: '';
    position: absolute;
    top: -6px;
    left: -6px;
    right: -6px;
    bottom: -6px;
    border: 3px solid #2196F3;
    border-radius: 12px;
    opacity: 0;
    transition: all 0.3s ease;
    pointer-events: none;
    z-index: -1;
    box-shadow: 0 0 15px rgba(33, 150, 243, 0.3);
}

.custom-node:hover::before {
    opacity: 1;
    box-shadow: 0 0 20px rgba(33, 150, 243, 0.4);
}

.custom-node:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(33, 150, 243, 0.2), 0 3px 6px rgba(33, 150, 243, 0.15);
}

.progress-bar-wrapper {
    position: absolute;
    top: -12px;
    left: 0;
    right: 0;
    z-index: 1;
}

.node-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    border-bottom: 2px solid;
}

.node-header-content {
    display: flex;
    flex-direction: column;
    padding: 0px 5px;
    flex: 1;
}

.node-title {
    font-weight: 600;
    color: #333;
    margin: 0;
    padding: 2px 10px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    transition: font-size 0.2s ease, line-height 0.2s ease;
}

.node-header-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
}

/* Tag Button */
.tag-button {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease, color 0.2s ease;
}

.tag-button:hover {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
}

.edit-button {
    width: 24px;
    height: 24px;
    border: none;
    border-radius: 6px;
    background: #3b82f6;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.delete-button {
    position: absolute;
    top: 10px; /* Changed from bottom to top for better visibility */
    right: 10px;
    width: 32px; /* Increased size for better visibility */
    height: 32px; /* Increased size for better visibility */
    border: none;
    border-radius: 6px;
    background: #ef4444;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000; /* Increased z-index for better visibility */
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3); /* Enhanced shadow for better visibility */
    pointer-events: auto !important; /* Ensure button is clickable */
}

.delete-button:hover {
    background: #dc2626;
    transform: scale(1.1); /* Enhanced scale effect on hover */
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.4); /* Enhanced shadow on hover */
}

.delete-button.disabled {
    background: #ccc;
    cursor: not-allowed;
    opacity: 0.7;
    box-shadow: none;
}

.delete-button.disabled:hover {
    background: #bbb;
    transform: none;
}

/* Delete floating menu styles */
.delete-floating-menu {
    position: fixed;
    display: flex;
    flex-direction: column;
    gap: 5px;
    background: transparent;
    z-index: 10000;
    pointer-events: auto;
}

.delete-floating-menu button {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    white-space: nowrap;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
    margin: 4px 0;
    pointer-events: auto;
}

.delete-floating-menu button.delete-node {
    background: #ef4444;
    color: white;
}

.delete-floating-menu button.delete-subtree {
    background: #dc2626;
    color: white;
}

.delete-floating-menu button.cancel {
    background: #9ca3af;
    color: white;
}

.delete-floating-menu button:hover {
    transform: scale(1.05);
}

.node-label {
    font-size: 8px;
    color: #666;
    margin-top: 2px;
}

.node-title {
    display: block;
    margin-bottom: 6px;
    font-size: 14px;
    border: none;
    background: transparent;
    width: 100%;
    outline: none;
    font-weight: bold;
}

.node-content {
    position: relative;
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    width: 100%;
    padding-bottom: 10px; /* Add extra padding at the bottom */
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
}

.add-child-button {
    position: absolute; /* Changed from relative to absolute for better positioning */
    bottom: -36px; /* Position below the node */
    left: 50%; /* Center horizontally */
    transform: translateX(-50%); /* Center horizontally */
    width: 48px;  /* Increased from 24px (100% larger) */
    height: 48px; /* Increased from 24px (100% larger) */
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;  /* Increased from 18px */
    line-height: 1;
    padding: 0;
    color: white;
    transition: transform 0.3s, background-color 0.2s, box-shadow 0.3s;
    background-color: #10b981 !important;
    z-index: 1000; /* Increased z-index for better visibility */
    box-shadow: 0 2px 8px rgba(0,0,0,0.3); /* Enhanced shadow for better visibility */
    transform-origin: center center;
    pointer-events: auto !important; /* Ensure it's always clickable */
}

.add-child-button:hover {
    transform: translateX(-50%) scale(1.1); /* Maintain horizontal centering while scaling */
    background-color: #059669 !important;
    box-shadow: 0 3px 10px rgba(0,0,0,0.4); /* Enhanced shadow on hover */
}

.node-content::-webkit-scrollbar {
    width: 6px;
}

.node-content::-webkit-scrollbar-track {
    background: transparent;
}

.node-content::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
    border: none;
}

.node-content:hover::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.4);
}

/* Backdrop for dimming the background when the editor is open */
.fullscreen-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9998;
    background-color: rgba(0, 0, 0, 0.5);
    overflow: hidden;
}

/* Centered "document-style" editor container */
.fullscreen-editor-container {
    position: fixed;
    top: 50%;
    left: 50%;
    width: 80%;
    max-width: 1000px;
    height: 80%;
    max-height: 90vh;
    transform: translate(-50%, -50%);
    background: #fff;
    border-radius: 6px;
    display: flex;
    flex-direction: column;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    overflow: visible !important;
    z-index: 9999;
}

/* Editor header bar */
.fullscreen-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #0074D9;
    padding: 12px 16px;
    color: white;
    font-weight: bold;
}

/* Close button in the header */
.fullscreen-close-btn {
    background-color: #ff4136;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    padding: 6px 12px;
    font-size: 14px;
}

/* Make the editor body flex so there's space for the buttons at the edges */
.fullscreen-editor-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 60px 40px; /* Top, Right, Bottom, Left */
    overflow: auto;
    margin-right: 60px; /* Make space for the speed dial */
}

/* Navigation Buttons positioned at the edges */
.nav-top-button {
    position: absolute;
    top: -20px; /* Reduced from -40px */
    left: 50%;
    transform: translateX(-50%);
    padding: 10px 20px;
    cursor: pointer;
    background-color: #555;
    color: #fff;
    border: none;
    border-radius: 4px;
}

.nav-left-button {
    position: absolute;
    top: 50%;
    left: -20px; /* Reduced from -40px */
    transform: translateY(-50%);
    padding: 10px 20px;
    cursor: pointer;
    background-color: #555;
    color: #fff;
    border: none;
    border-radius: 4px;
}

.nav-right-button {
    position: absolute;
    top: 50%;
    right: -20px; /* Reduced from -40px */
    transform: translateY(-50%);
    padding: 10px 20px;
    cursor: pointer;
    background-color: #555;
    color: #fff;
    border: none;
    border-radius: 4px;
}

.nav-bottom-button {
    position: absolute;
    bottom: -20px; /* Reduced from -40px */
    left: 50%;
    transform: translateX(-50%);
    padding: 10px 20px;
    cursor: pointer;
    background-color: #555;
    color: #fff;
    border: none;
    border-radius: 4px;
}

/* Ensure buttons have hover effects */
.nav-top-button:hover,
.nav-left-button:hover,
.nav-right-button:hover,
.nav-bottom-button:hover {
    background-color: #333;
}

/* Adjust for smaller screens */
@media (max-width: 600px) {
    .fullscreen-editor-container {
        width: 95%;
        height: 85%;
        max-width: 100%;
        max-height: 100vh;
    }

    .fullscreen-header {
        padding: 10px 12px;
    }

    .fullscreen-close-btn {
        padding: 4px 10px;
        font-size: 12px;
    }

    .fullscreen-editor-body {
        padding: 60px 16px; /* Increased padding for button space */
    }

    .nav-top-button,
    .nav-left-button,
    .nav-right-button,
    .nav-bottom-button {
        padding: 8px 16px;
        font-size: 12px;
    }
}

.p-speeddial-button.p-button.p-button-icon-only {
    width: 2.5rem;
    height: 2.5rem;
    background-color: rgba(255, 255, 255, 0.9);
    border: 1px solid #ddd;
}

.p-speeddial-action {
    background-color: rgba(255, 255, 255, 0.9) !important;
    border: 1px solid #ddd !important;
    margin-bottom: 0.5rem;
}

.p-speeddial-action:hover {
    background-color: #f8f9fa !important;
}

.node-speeddial .p-speeddial-action {
    pointer-events: all !important;
}

.node-speeddial .p-speeddial-button {
    z-index: 1001 !important;
}

.p-speeddial-list {
    gap: 0.5rem !important;
}
.color-picker-wrapper {
    position: absolute;
    top: 8px;
    right: 8px;
    z-index: 1000;
}

.color-picker-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin-right: 4px;
    padding: 0;
}

.color-picker-button:hover {
    transform: scale(1.1);
}

.color-picker-icon {
    font-size: 14px;
}

.color-picker-popup {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 8px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 99999;
    pointer-events: auto;
}

.color-picker-close {
    margin-top: 10px;
    padding: 6px 12px;
    background: #0074D9;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.color-picker-close:hover {
    background: #0056b3;
}

.custom-node-container {
    position: relative;
}

/* New button strip on the right side of the node */
.node-button-strip {
    position: absolute;
    top: 10px;           /* Adjust vertical alignment as needed */
    right: -40px;        /* Positions the strip to the right of the node */
    display: flex;
    flex-direction: column;
    gap: 8px;            /* Spacing between buttons */
}

/* Styles for the square action buttons */
.node-action-button {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #0074D9;
    border: none;
    color: white;
    cursor: pointer;
    border-radius: 2px;  /* Slightly rounded corners */
    font-size: 14px;
    line-height: 24px;
}

.node-action-button:hover {
    background-color: #0056b3;
}

.custom-node button {
    cursor: pointer;
}

/* Ensure PrimeReact color picker is visible */
.p-colorpicker-panel {
    z-index: 99999 !important;
}

/* Style the color picker overlay */
.p-component-overlay {
    z-index: 99998 !important;
}

.color-controls {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 8px;
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    margin-right: 4px;
    box-shadow: 0 0 4px rgba(0, 0, 0, 0.1);
    transition: background-color 0.2s ease;
}

.color-controls:hover {
    background-color: rgba(255, 255, 255, 0.5);
}

.color-checkbox-label {
    font-size: 14px;
    font-weight: bold;
    color: #333;
    cursor: pointer;
    user-select: none;
    transition: transform 0.2s ease;
    padding: 2px 4px;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.5);
}

.color-checkbox-label:hover {
    transform: scale(1.05);
    background-color: rgba(255, 255, 255, 0.8);
}

.color-inherit-checkbox {
    width: 24px;
    height: 24px;
    cursor: pointer !important;
    margin: 0;
    border: 2px solid #0074D9;
    border-radius: 4px;
    position: relative;
    z-index: 10;
    transform: scale(1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    appearance: none;
    -webkit-appearance: none;
    background-color: white;
}

.color-inherit-checkbox:checked {
    background-color: #0074D9;
    border-color: #0074D9;
}

.color-inherit-checkbox:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 16px;
    font-weight: bold;
}

.color-inherit-checkbox:hover {
    transform: scale(1.1);
    box-shadow: 0 0 8px rgba(0, 116, 217, 0.5);
}

.color-picker-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px; /* Increased size for better visibility */
    height: 32px; /* Increased size for better visibility */
    border: 2px solid white;
    border-radius: 6px;
    cursor: pointer !important;
    padding: 0;
    opacity: 1;
    transition: all 0.2s ease;
    position: relative;
    z-index: 1001 !important; /* Increased z-index for better visibility */
    box-shadow: 0 2px 8px rgba(0,0,0,0.3); /* Added shadow for better visibility */
    pointer-events: auto !important; /* Ensure button is clickable */
}

.color-picker-button:hover {
    transform: scale(1.1);
    box-shadow: 0 3px 10px rgba(0,0,0,0.4); /* Enhanced shadow on hover */
}

.color-picker-icon {
    font-size: 16px;
}

.color-picker-button.disabled {
    opacity: 0.5;
    pointer-events: none;
}

/* Remove zoom-dependent styles as we're now using dynamic inline styles */

.node-speeddial {
  position: absolute;
  bottom: -36px; /* Adjusted from -28px to account for larger button */
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000; /* Increased z-index for better visibility */
  padding: 8px;
  transform-origin: center bottom;
  transition: transform 0.3s;
  pointer-events: auto !important; /* Ensure buttons are clickable */
  overflow: visible !important; /* Ensure menu is visible */
}

.node-speeddial .p-speeddial {
  position: relative;
  pointer-events: auto !important; /* Ensure buttons are clickable */
  overflow: visible !important; /* Ensure menu is visible */
}

.node-speeddial .p-speeddial-button {
  width: 36px !important; /* Increased size for better visibility */
  height: 36px !important; /* Increased size for better visibility */
  background-color: #10b981 !important;
  box-shadow: 0 2px 8px rgba(0,0,0,0.3) !important; /* Added shadow for better visibility */
  pointer-events: auto !important; /* Ensure buttons are clickable */
  z-index: 1001 !important; /* Ensure button is above other elements */
}

.node-speeddial .p-speeddial-button:hover {
  background-color: #059669 !important;
  transform: scale(1.1); /* Added scale effect on hover */
}

.node-speeddial .p-speeddial-action {
  width: 32px !important;
  height: 32px !important;
  background-color: white !important;
  border: 1px solid #e2e8f0 !important;
  color: #1f2937 !important;
  box-shadow: 0 2px 8px rgba(0,0,0,0.2) !important; /* Enhanced shadow */
  margin-bottom: 8px !important;
  pointer-events: auto !important; /* Ensure buttons are clickable */
  z-index: 1001 !important; /* Ensure buttons are above other elements */
}

.node-speeddial .p-speeddial-action:hover {
  background-color: #f8fafc !important;
  transform: scale(1.05);
}

.node-speeddial .p-speeddial-list {
  padding: 0;
  margin: 0;
  list-style: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-bottom: 8px;
  pointer-events: auto !important; /* Ensure menu items are clickable */
  z-index: 1001 !important; /* Ensure menu is above other elements */
  overflow: visible !important; /* Ensure menu is visible */
}

.node-type-menu {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.25);
    padding: 8px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    min-width: 120px;
    z-index: 1002; /* Higher z-index than the button */
    padding-bottom: 16px;
    margin-bottom: -8px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: bottom center;
    will-change: transform;
    backface-visibility: hidden;
    -webkit-font-smoothing: subpixel-antialiased;
    pointer-events: auto; /* Ensure menu items are clickable */
    border: 1px solid rgba(0,0,0,0.1);
}

.node-type-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  border: none;
  background: white;
  color: #1f2937;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  width: 100%;
  text-align: left;
  white-space: nowrap;
  backface-visibility: hidden;
  -webkit-font-smoothing: subpixel-antialiased;
}

.node-type-item:hover {
  background-color: #f3f4f6;
  transform: translateX(2px);
}

.node-type-icon {
  font-size: 16px;
  width: 20px;
  text-align: center;
  flex-shrink: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.node-type-label {
  font-size: 14px;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.node-type-menu-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
}

/* Create a hover bridge between button and menu but don't block button clicks */
.node-type-menu::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    right: 0;
    height: 8px;
    background: transparent;
    pointer-events: none; /* This allows clicks to pass through to elements beneath */
}

.scissors-button {
    position: absolute;
    bottom: 10px;
    left: 10px;
    width: 24px;
    height: 24px;
    border: none;
    border-radius: 6px;
    background: #3b82f6;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.scissors-button:hover {
    background: #2563eb;
}

.scissors-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.scissors-modal-content {
    background: white;
    padding: 20px;
    border-radius: 8px;
    max-width: 400px;
    width: 90%;
    pointer-events: auto;
}

.scissors-modal-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 20px;
}

.scissors-modal-button {
    padding: 12px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    pointer-events: auto;
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 14px;
    transition: background-color 0.2s;
}

.scissors-modal-button.cancel {
    background: #e5e7eb;
}

.scissors-modal-button.cancel:hover {
    background: #d1d5db;
}

.scissors-modal-button.cut {
    background: #3b82f6;
    color: white;
}

.scissors-modal-button.cut:hover {
    background: #2563eb;
}

.scissors-modal-button.cut-with-subtree {
    background: #2563eb;
    color: white;
}

.scissors-modal-button.cut-with-subtree:hover {
    background: #1d4ed8;
}

.cut-icon {
    position: relative;
    width: 40px;
    height: 40px;
}

.cut-icon.single {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.cut-icon.single .node-icon {
    font-size: 16px;
    margin-bottom: 4px;
}

.cut-icon.single .line {
    width: 2px;
    height: 12px;
    background-color: currentColor;
}

.cut-icon.tree {
    position: relative;
}

.cut-icon.tree .node-icon {
    position: absolute;
    font-size: 16px;
}

.cut-icon.tree .node-icon.top {
    top: 0;
    left: 50%;
    transform: translateX(-50%);
}

.cut-icon.tree .node-icon.bottom-left {
    bottom: 0;
    left: 8px;
}

.cut-icon.tree .node-icon.bottom-right {
    bottom: 0;
    right: 8px;
}

.cut-icon.tree .line-tree {
    position: absolute;
    top: 12px;
    left: 50%;
    transform: translateX(-50%);
    width: 2px;
    height: 12px;
    background-color: currentColor;
}

.cut-icon.tree .line-tree:before,
.cut-icon.tree .line-tree:after {
    content: '';
    position: absolute;
    bottom: 0;
    width: 16px;
    height: 2px;
    background-color: currentColor;
}

.cut-icon.tree .line-tree:before {
    right: 0;
    transform: rotate(45deg);
    transform-origin: bottom right;
}

.cut-icon.tree .line-tree:after {
    left: 0;
    transform: rotate(-45deg);
    transform-origin: bottom left;
}

.insert-button {
    position: absolute;
    bottom: 10px;
    left: 10px;
    width: 24px;
    height: 24px;
    border: none;
    border-radius: 6px;
    background: #10b981;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    transition: background-color 0.2s;
}

.insert-button:hover {
    background: #059669;
}

.insert-button:disabled {
    background: #9ca3af;
    cursor: not-allowed;
    opacity: 0.6;
}

.paste-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.paste-modal-content {
    background: white;
    padding: 20px;
    border-radius: 8px;
    max-width: 400px;
    width: 90%;
    pointer-events: auto;
}

.paste-modal-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

.paste-modal-button {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    pointer-events: auto;
    font-size: 14px;
    transition: background-color 0.2s;
}

.paste-modal-button.cancel {
    background: #e5e7eb;
}

.paste-modal-button.cancel:hover {
    background: #d1d5db;
}

.paste-modal-button.paste {
    background: #10b981;
    color: white;
}

.paste-modal-button.paste:hover {
    background: #059669;
}

/* Add this new ruleset to optimize animations */
.custom-node * {
  backface-visibility: hidden;
  -webkit-font-smoothing: subpixel-antialiased;
}

/* Add a keyframe animation for the border highlight */
@keyframes dashoffset {
  from {
    stroke-dashoffset: 0;
  }
  to {
    stroke-dashoffset: 12;
  }
}

/* Animation for the pulse effect */
@keyframes pulse {
  0% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
  100% {
    opacity: 0.4;
    transform: scale(1);
  }
}

/* Collapse/expand button */
.collapse-button {
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 50%;
  background: rgba(59, 130, 246, 0.8);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  left: -12px;
  top: 12px; /* Fixed position at the top of the node header */
  transform: translateY(0);
  transition: transform 0.3s ease, background-color 0.3s ease;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.collapse-button:hover {
  background: rgba(59, 130, 246, 1);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
}

.collapse-button.collapsed {
  transform: rotate(-90deg);
}

/* Collapsed node styling */
.collapsed-node {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3), 0 4px 8px rgba(0, 0, 0, 0.2);
}

.collapsed-node::after {
  content: '⋯';
  position: absolute;
  bottom: -10px;
  right: 50%;
  transform: translateX(50%);
  background: #3B82F6;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 10;
}

/* Title node styling */
.custom-node.title {
  border-radius: 50px !important;
  background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(240,240,240,0.9));
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 30px;
  min-height: 80px;
  max-height: 100px;
  overflow: visible !important;
  width: 450px !important;
  border: 2px solid #ddd !important;
}

/* Hide node content for title nodes */
.custom-node.title .node-content {
  display: none !important;
}

/* Hide content container for title nodes */
.custom-node.title .node-content-container {
  display: none !important;
}

/* Position buttons for title nodes */
.custom-node.title .delete-button {
  position: absolute !important;
  top: 5px !important;
  right: 5px !important;
  display: flex !important;
  z-index: 100 !important;
}

.custom-node.title .bottom-left-controls {
  position: absolute !important;
  bottom: 5px !important;
  left: 5px !important;
  display: flex !important;
  z-index: 100 !important;
}

/* Hide completable checkbox for title nodes */
.custom-node.title .completable-checkbox {
  display: none !important;
}

/* Position the node type menu outside the node */
.custom-node.title .node-type-menu {
  bottom: -200px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  z-index: 1000 !important;
}

/* Style the header for title nodes */
.custom-node.title .node-header {
  border-bottom: none !important;
  padding: 0 !important;
  margin: 0 !important;
  width: 100% !important;
  display: flex !important;
  justify-content: center !important;
  background-color: transparent !important;
}

/* Style the title text */
.custom-node.title .node-title {
  font-size: 32px !important;
  font-weight: 700 !important;
  text-align: center !important;
  color: #333 !important;
  padding: 0 !important;
  margin: 0 !important;
  line-height: 1.2 !important;
  cursor: pointer !important;
}

/* Position the add child button outside the node */
.custom-node.title .node-speeddial {
  position: absolute !important;
  bottom: -30px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  z-index: 100 !important;
}

/* Hide tag selector and tags for title nodes */
.custom-node.title .hover-tag-selector-container,
.custom-node.title .node-tags-container,
.custom-node.title .tag-button {
  display: none !important;
}

/* Completed node styling */
.completed-node {
  position: relative;
}

.completion-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(34, 197, 94, 0.2); /* Light green transparent overlay */
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none; /* Allow clicks to pass through to the node */
  border-radius: 8px;
}

/* Bottom left controls container */
.bottom-left-controls {
  position: absolute;
  bottom: 10px;
  left: 10px;
  display: flex;
  align-items: center;
  gap: 10px;
  z-index: 1000; /* Increased z-index for better visibility */
  pointer-events: auto !important; /* Ensure buttons are clickable */
}

.completable-checkbox {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: white;
  border: 2px solid #ccc;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0,0,0,0.3); /* Enhanced shadow for better visibility */
  transition: all 0.2s ease;
  pointer-events: auto !important; /* Ensure button is clickable */
  z-index: 1001 !important; /* Ensure button is above other elements */
}

.completable-checkbox:hover {
  transform: scale(1.1);
  box-shadow: 0 3px 10px rgba(0,0,0,0.4); /* Enhanced shadow on hover */
}

/* Adjust color controls for bottom placement */
.bottom-left-controls .color-controls {
  display: flex;
  align-items: center;
  gap: 5px;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 4px 8px;
  border-radius: 18px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
  transition: all 0.2s ease;
}

.bottom-left-controls .color-controls:hover {
  transform: scale(1.05);
  box-shadow: 0 3px 6px rgba(0,0,0,0.3);
}

/* Adjust color picker button for bottom placement */
.bottom-left-controls .color-picker-button {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
  transition: all 0.2s ease;
}

.bottom-left-controls .color-picker-button:hover {
  transform: scale(1.1);
  box-shadow: 0 3px 6px rgba(0,0,0,0.3);
}

.bottom-left-controls .color-picker-icon {
  font-size: 14px;
}

.bottom-left-controls .color-checkbox-label {
  font-size: 14px;
  color: #333;
  margin-left: 4px;
  cursor: pointer;
  padding: 2px 4px;
  border-radius: 4px;
  background-color: transparent;
  transition: transform 0.2s ease;
}

.bottom-left-controls .color-checkbox-label:hover {
  transform: scale(1.05);
}

.bottom-left-controls .color-inherit-checkbox {
  width: 20px;
  height: 20px;
  cursor: pointer !important;
  margin: 0;
  border: 2px solid #0074D9;
  border-radius: 4px;
  position: relative;
  z-index: 10;
  transform: scale(1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  appearance: none;
  -webkit-appearance: none;
  background-color: white;
}

.bottom-left-controls .color-inherit-checkbox:checked {
  background-color: #0074D9;
  border-color: #0074D9;
}

.bottom-left-controls .color-inherit-checkbox:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 14px;
  font-weight: bold;
}

.completed-node .completable-checkbox {
  background-color: rgba(34, 197, 94, 0.8);
  border-color: rgba(34, 197, 94, 0.8);
}

.completed-node .completable-checkbox::after {
  content: '\2713'; /* Unicode checkmark */
  color: white;
  font-size: 22px;
  font-weight: bold;
}
