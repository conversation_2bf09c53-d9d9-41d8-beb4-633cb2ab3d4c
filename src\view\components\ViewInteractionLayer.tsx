/**
 * ViewInteractionLayer - Component for handling global mouse/touch interactions
 *
 * This component provides a centralized way to handle mouse and touch events
 * for node interactions, eliminating race conditions and stale references.
 *
 * Node Dragging Implementation:
 * - On mouse down, we calculate and store the offset between cursor world position and node position
 * - On mouse move, we calculate the cursor's new world position and apply the stored offset
 * - This ensures nodes follow the cursor with a 1:1 relationship regardless of zoom level
 */

import React, { useEffect, useRef, useState, useCallback, createContext } from 'react';
import { nodeStore, NodeStoreEvents } from '../../model/NodeStore';
import { NodeModel } from '../../model/NodeModel';
import { QuadTree } from '../../utils/QuadTree';
import { nodeController } from '../../controller/NodeController';
import { layoutController } from '../../controller/LayoutController';
import { zoomController } from '../../controller/ZoomController';
import { transformController } from '../../controller/TransformController';
import RelinkModal from './RelinkModal';
import RelinkPathVisualizer from './RelinkPathVisualizer';
import { useGraph } from '../../context/GraphContext';
import { useNodeInteraction } from '../../context/NodeInteractionContext';
import { useTransform } from '../../context/TransformContext';
import { CoordinateConverter } from '../../utils/CoordinateConverter';

// Create a context for the highlighted node
export const HighlightedNodeContext = createContext<{
  highlightedNodeId: string | null;
  registerHighlightedNode: (nodeId: string | null) => void;
}>({
  highlightedNodeId: null,
  registerHighlightedNode: () => {}
});

interface ViewInteractionLayerProps {
  // Will add more props as needed
}

export const ViewInteractionLayer: React.FC<ViewInteractionLayerProps> = () => {
  // Get the setSelectedNodeId function from GraphContext
  const { setSelectedNodeId } = useGraph();

  // Get the openNodeEditor function from NodeInteractionContext
  const { openNodeEditor } = useNodeInteraction();

  // Get transform from TransformContext
  const { transform } = useTransform();

  // CRITICAL FIX: Create a ref to always store the latest transform
  const transformRef = useRef(transform);

  // CRITICAL FIX: Update the ref whenever transform changes
  useEffect(() => {
    transformRef.current = transform;

    // CRITICAL FIX: Also update the global mvcTransform for legacy components
    if (window.mvcTransform) {
      window.mvcTransform.scale = transform.scale;
      window.mvcTransform.offset = { ...transform.offset };
      console.log(`[ViewInteractionLayer] Updated global mvcTransform: scale=${transform.scale.toFixed(6)}, offset=(${transform.offset.x.toFixed(2)}, ${transform.offset.y.toFixed(2)})`);
    }
  }, [transform]);

  // Log the transform when it's first received
  useEffect(() => {
    console.log(`[ViewInteractionLayer] Initial transform: scale=${transform.scale.toFixed(6)}, offset=(${transform.offset.x.toFixed(2)}, ${transform.offset.y.toFixed(2)})`);

    // Verify the transform is valid
    if (typeof transform.scale !== 'number' || isNaN(transform.scale)) {
      console.error(`[ViewInteractionLayer] Invalid transform scale: ${transform.scale}`);
    }

    if (!transform.offset || typeof transform.offset.x !== 'number' || typeof transform.offset.y !== 'number') {
      console.error(`[ViewInteractionLayer] Invalid transform offset: ${JSON.stringify(transform.offset)}`);
    }

    // CRITICAL FIX: Listen for custom transform scale change events
    const handleTransformScaleChanged = (e: Event) => {
      // Type assertion to access the detail property
      const customEvent = e as CustomEvent<{scale: number}>;

      console.log(`[ViewInteractionLayer] Received custom transform scale change event: ${customEvent.detail.scale.toFixed(6)}`);

      // Force update the coordinate debug if it's visible
      if ((window as any).debugCoordinates && document.getElementById('coordinate-debug')) {
        const lastMousePos = { x: 0, y: 0 };

        // Get the last mouse position from the document if available
        if (document.body.getAttribute('data-last-mouse-x') && document.body.getAttribute('data-last-mouse-y')) {
          lastMousePos.x = parseInt(document.body.getAttribute('data-last-mouse-x') || '0');
          lastMousePos.y = parseInt(document.body.getAttribute('data-last-mouse-y') || '0');
        }

        // Update the debug visualization with the latest transform
        showCoordinateDebug(lastMousePos.x, lastMousePos.y);
      }
    };

    window.addEventListener('mvc-transform-scale-changed', handleTransformScaleChanged);

    return () => {
      window.removeEventListener('mvc-transform-scale-changed', handleTransformScaleChanged);
    };
  }, []);

  // State for relinking
  const [showRelinkModal, setShowRelinkModal] = useState(false);
  const [relinkModalPosition, setRelinkModalPosition] = useState({ x: 0, y: 0 });
  const [draggedNode, setDraggedNode] = useState<NodeModel | null>(null);
  const [targetNode, setTargetNode] = useState<NodeModel | null>(null);
  const [showRelinkPath, setShowRelinkPath] = useState(false);

  // State for highlighted node
  const [highlightedNodeId, setHighlightedNodeId] = useState<string | null>(null);

  // References to track state
  const isMouseDownRef = useRef(false);
  const lastEventTimeRef = useRef<number>(0);
  const quadTreeRef = useRef<QuadTree | null>(null);
  const draggedNodeRef = useRef<string | null>(null);
  const mouseDownTimeRef = useRef<number>(0);
  const mouseDownPosRef = useRef<{x: number, y: number} | null>(null);
  // Add ref to track initial positions and offsets for dragging
  const dragStartRef = useRef<{
    mouseX: number,
    mouseY: number,
    nodeX: number,
    nodeY: number,
    // World coordinates of initial mouse position
    mouseWorldX: number,
    mouseWorldY: number,
    // Add cursor-to-node offset in world coordinates
    offsetX: number,
    offsetY: number,
    // Store initial scale for reference
    initialScale?: number
  } | null>(null);

  // Constants for click vs. drag detection
  const CLICK_TIME_THRESHOLD = 300; // ms
  const CLICK_DISTANCE_THRESHOLD = 10; // pixels

  // Ref to track potential relink target
  const potentialRelinkTargetRef = useRef<NodeModel | null>(null);

  // Ref to store the original target element when mouse down occurs
  const originalTargetRef = useRef<EventTarget | null>(null);

  // Function to register a highlighted node
  const registerHighlightedNode = useCallback((nodeId: string | null) => {
    setHighlightedNodeId(nodeId);
    console.log(`[HoverHitDetection] Node highlight changed: ${nodeId || 'none'}`);
  }, []);

  /**
   * Show coordinate debug visualization - disabled for production
   * @param screenX Screen X coordinate
   * @param screenY Screen Y coordinate
   * @param nodePosition Optional node position for comparison
   */
  const showCoordinateDebug = (screenX: number, screenY: number, nodePosition?: { x: number, y: number }) => {
    // Debug visualization has been disabled for production
    // Only store the mouse position for compatibility with other components
    document.body.setAttribute('data-last-mouse-x', screenX.toString());
    document.body.setAttribute('data-last-mouse-y', screenY.toString());

    // Set debugCoordinates to false to ensure debug panel doesn't appear
    (window as any).debugCoordinates = false;

    // Remove any existing debug element
    const existingDebugEl = document.getElementById('coordinate-debug');
    if (existingDebugEl && existingDebugEl.parentNode) {
      existingDebugEl.parentNode.removeChild(existingDebugEl);
    }

    // Debug panel content has been disabled for production
  };

  /**
   * Convert screen coordinates to world/canvas coordinates
   * Using CoordinateConverter as the single source of truth
   * @param screenX Screen X coordinate
   * @param screenY Screen Y coordinate
   * @returns Canvas coordinates
   */
  const screenToWorld = (screenX: number, screenY: number) => {
    // CRITICAL FIX: Always use the current transform from TransformContext via the ref
    // This ensures we're using the latest scale value
    const currentTransform = transformRef.current;

    // CRITICAL FIX: Also check the global mvcTransform for the latest scale
    if (window.mvcTransform && window.mvcTransform.scale) {
      const globalScale = window.mvcTransform.scale;
      if (Math.abs(globalScale - currentTransform.scale) > 0.0001) {
        console.warn(`[CoordinateTransform] Scale mismatch detected! Local: ${currentTransform.scale.toFixed(6)}, Global: ${globalScale.toFixed(6)}. Using global scale.`);
        currentTransform.scale = globalScale;
      }
    }

    const result = CoordinateConverter.screenToCanvas(screenX, screenY, currentTransform);

    // Log the transformation for debugging
    console.log(`[CoordinateTransform] Screen (${screenX}, ${screenY}) → Canvas (${result.x.toFixed(6)}, ${result.y.toFixed(6)}) | Scale: ${currentTransform.scale.toFixed(6)}, Offset: (${currentTransform.offset.x.toFixed(2)}, ${currentTransform.offset.y.toFixed(2)})`);

    return result;
  };

  /**
   * Convert world/canvas coordinates to screen coordinates
   * Using CoordinateConverter as the single source of truth
   * @param canvasX Canvas X coordinate
   * @param canvasY Canvas Y coordinate
   * @returns Screen coordinates
   */
  const worldToScreen = (canvasX: number, canvasY: number) => {
    // CRITICAL FIX: Always use the current transform from TransformContext via the ref
    // This ensures we're using the latest scale value
    const currentTransform = transformRef.current;

    // CRITICAL FIX: Also check the global mvcTransform for the latest scale
    if (window.mvcTransform && window.mvcTransform.scale) {
      const globalScale = window.mvcTransform.scale;
      if (Math.abs(globalScale - currentTransform.scale) > 0.0001) {
        console.warn(`[CoordinateTransform] Scale mismatch detected! Local: ${currentTransform.scale.toFixed(6)}, Global: ${globalScale.toFixed(6)}. Using global scale.`);
        currentTransform.scale = globalScale;
      }
    }

    return CoordinateConverter.canvasToScreen(canvasX, canvasY, currentTransform);
  };

  /**
   * In our system, canvas coordinates are the same as world coordinates
   * This function is kept for backward compatibility
   * @param worldX World X coordinate
   * @param worldY World Y coordinate
   * @returns Canvas coordinates
   */
  const worldToCanvas = (worldX: number, worldY: number) => {
    // Canvas coordinates are the same as world coordinates in this system
    return { x: worldX, y: worldY };
  };

  // Set up effect to build quadtree and listen for node changes
  useEffect(() => {
    console.log('[ViewInteractionLayer] Setting up quadtree');

    // Build the initial quadtree
    buildQuadTree();

    // Subscribe to node store changes
    const unsubscribeNodeAdded = nodeStore.on(NodeStoreEvents.NODE_ADDED, () => {
      console.log('[ViewInteractionLayer] Node added, rebuilding quadtree');
      buildQuadTree();
    });

    const unsubscribeNodeUpdated = nodeStore.on(NodeStoreEvents.NODE_UPDATED, () => {
      console.log('[ViewInteractionLayer] Node updated, rebuilding quadtree');
      buildQuadTree();
    });

    const unsubscribeNodeRemoved = nodeStore.on(NodeStoreEvents.NODE_REMOVED, () => {
      console.log('[ViewInteractionLayer] Node removed, rebuilding quadtree');
      buildQuadTree();
    });

    // Clean up subscriptions
    return () => {
      console.log('[ViewInteractionLayer] Cleaning up node store subscriptions');
      unsubscribeNodeAdded();
      unsubscribeNodeUpdated();
      unsubscribeNodeRemoved();
    };
  }, []);

  // Keep track of transform changes for debugging
  useEffect(() => {
    // CRITICAL FIX: Update the transform ref with the latest transform
    transformRef.current = transform;

    // CRITICAL FIX: Also update the global mvcTransform for legacy components
    if (window.mvcTransform) {
      window.mvcTransform.scale = transform.scale;
      window.mvcTransform.offset = { ...transform.offset };
    }

    // Verify the transform is valid
    if (typeof transform.scale !== 'number' || isNaN(transform.scale)) {
      console.error(`[ViewInteractionLayer] Invalid transform scale in effect: ${transform.scale}`);
    }

    if (!transform.offset || typeof transform.offset.x !== 'number' || typeof transform.offset.y !== 'number') {
      console.error(`[ViewInteractionLayer] Invalid transform offset in effect: ${JSON.stringify(transform.offset)}`);
    }

    // Log transform changes
    console.log(`[ViewInteractionLayer] Transform changed: scale=${transform.scale.toFixed(6)}, offset=(${transform.offset.x.toFixed(2)}, ${transform.offset.y.toFixed(2)})`);

    // CRITICAL FIX: Store the current mouse position in document attributes
    // This allows other components to access it when needed
    document.body.setAttribute('data-current-transform-scale', transform.scale.toString());
    document.body.setAttribute('data-current-transform-offset-x', transform.offset.x.toString());
    document.body.setAttribute('data-current-transform-offset-y', transform.offset.y.toString());

    // Log transform changes during dragging
    if (draggedNodeRef.current && dragStartRef.current) {
      console.log(`[CoordinateDebug] Transform changed during drag: scale=${transform.scale.toFixed(6)}, offset=(${transform.offset.x.toFixed(2)}, ${transform.offset.y.toFixed(2)})`);

      // Update debug visualization when transform changes during dragging
      const node = nodeStore.getNode(draggedNodeRef.current);
      if (node) {
        // Calculate current mouse position (use last known position)
        const lastMousePos = { x: 0, y: 0 };

        // Get the last mouse position from the document if available
        if (document.body.getAttribute('data-last-mouse-x') && document.body.getAttribute('data-last-mouse-y')) {
          lastMousePos.x = parseInt(document.body.getAttribute('data-last-mouse-x') || '0');
          lastMousePos.y = parseInt(document.body.getAttribute('data-last-mouse-y') || '0');
        }

        // Log the inputs to calculateNewNodePosition
        console.log(`[CoordinateDebug] Inputs to calculateNewNodePosition:`);
        console.log(`[CoordinateDebug] Initial node: (${dragStartRef.current.nodeX.toFixed(2)}, ${dragStartRef.current.nodeY.toFixed(2)})`);
        console.log(`[CoordinateDebug] Initial mouse: (${dragStartRef.current.mouseX.toFixed(2)}, ${dragStartRef.current.mouseY.toFixed(2)})`);
        console.log(`[CoordinateDebug] Current mouse: (${lastMousePos.x.toFixed(2)}, ${lastMousePos.y.toFixed(2)})`);
        console.log(`[CoordinateDebug] Transform: scale=${transform.scale.toFixed(6)}, offset=(${transform.offset.x.toFixed(2)}, ${transform.offset.y.toFixed(2)})`);

        // CRITICAL FIX: Use the current transform from transformRef
        // This ensures we're using the latest scale value
        const currentTransform = transformRef.current;

        // Calculate new node position using CoordinateConverter
        const nodePosition = CoordinateConverter.calculateNewNodePosition(
          dragStartRef.current.nodeX,
          dragStartRef.current.nodeY,
          dragStartRef.current.mouseX,
          dragStartRef.current.mouseY,
          lastMousePos.x,
          lastMousePos.y,
          currentTransform // Use current transform with correct scale
        );

        // Update debug visualization
        showCoordinateDebug(lastMousePos.x, lastMousePos.y, nodePosition);
      }
    }

    // CRITICAL FIX: Update any visible coordinate debug window
    if ((window as any).debugCoordinates && document.getElementById('coordinate-debug')) {
      const lastMousePos = { x: 0, y: 0 };

      // Get the last mouse position from the document if available
      if (document.body.getAttribute('data-last-mouse-x') && document.body.getAttribute('data-last-mouse-y')) {
        lastMousePos.x = parseInt(document.body.getAttribute('data-last-mouse-x') || '0');
        lastMousePos.y = parseInt(document.body.getAttribute('data-last-mouse-y') || '0');
      }

      // Update the debug visualization with the latest transform
      showCoordinateDebug(lastMousePos.x, lastMousePos.y);
    }
  }, [transform]);

  // Debug button for coordinate debug has been disabled
  // useEffect(() => {
  //   // Create debug button - disabled for production
  // }, []);

  /**
   * Build or rebuild the quadtree with current nodes
   */
  const buildQuadTree = () => {
    // Get all nodes
    const nodes = nodeStore.getAllNodes();

    if (nodes.length === 0) {
      quadTreeRef.current = null;
      return;
    }

    // Find the bounds of all nodes to create an appropriate quadtree
    let minX = Infinity;
    let minY = Infinity;
    let maxX = -Infinity;
    let maxY = -Infinity;

    for (const node of nodes) {
      const { position, dimensions } = node;
      minX = Math.min(minX, position.x);
      minY = Math.min(minY, position.y);
      maxX = Math.max(maxX, position.x + dimensions.width);
      maxY = Math.max(maxY, position.y + dimensions.height);
    }

    // Add padding to the bounds
    const padding = 1000; // Extra space to accommodate new nodes
    minX -= padding;
    minY -= padding;
    maxX += padding;
    maxY += padding;

    // Create a new quadtree with the calculated bounds
    const bounds = {
      x: minX,
      y: minY,
      width: maxX - minX,
      height: maxY - minY
    };

    quadTreeRef.current = new QuadTree(bounds, 4, 10);

    // Insert all nodes into the quadtree
    for (const node of nodes) {
      quadTreeRef.current.insert({
        id: node.id,
        x: node.position.x,
        y: node.position.y,
        width: node.dimensions.width,
        height: node.dimensions.height
      });
    }

    console.log(`[ViewInteractionLayer] Built quadtree with ${nodes.length} nodes`);
  };

  /**
   * Find the node at the given screen coordinates using hover-based hit detection
   * @param screenX Screen X coordinate
   * @param screenY Screen Y coordinate
   * @param strictMode If true, only return direct hits (no proximity detection)
   * @returns The node at the coordinates, or null if none
   */
  const findNodeAtPosition = (screenX: number, screenY: number, strictMode: boolean = false): NodeModel | null => {
    console.log(`[EventFlow] ViewInteractionLayer.findNodeAtPosition: screenX=${screenX}, screenY=${screenY}, strictMode=${strictMode}`);

    // ALWAYS check if we have a highlighted node first, regardless of strictMode
    // This is the key to hover-based hit detection
    if (highlightedNodeId) {
      const highlightedNode = nodeStore.getNode(highlightedNodeId);
      if (highlightedNode) {
        // When we have a highlighted node, we need to verify it's actually under the cursor
        // by checking if the point is inside the node's DOM element
        const nodeElement = document.querySelector(`[data-node-id="${highlightedNodeId}"]`);
        if (nodeElement) {
          const rect = nodeElement.getBoundingClientRect();
          const isInside = (
            screenX >= rect.left &&
            screenX <= rect.right &&
            screenY >= rect.top &&
            screenY <= rect.bottom
          );

          // Log distance from click to node boundaries for debugging
          const distanceTop = screenY - rect.top;
          const distanceBottom = rect.bottom - screenY;
          const distanceLeft = screenX - rect.left;
          const distanceRight = rect.right - screenX;

          console.log(`[EventFlow] ViewInteractionLayer.findNodeAtPosition: Highlighted node DOM check: ${highlightedNodeId} (${highlightedNode.title}), isInside=${isInside}, distances: top=${distanceTop.toFixed(1)}, bottom=${distanceBottom.toFixed(1)}, left=${distanceLeft.toFixed(1)}, right=${distanceRight.toFixed(1)}`);

          if (isInside) {
            console.log(`[EventFlow] ViewInteractionLayer.findNodeAtPosition: USING HIGHLIGHTED NODE ${highlightedNodeId} (${highlightedNode.title}) - confirmed with DOM check`);
            return highlightedNode;
          } else {
            console.log(`[EventFlow] ViewInteractionLayer.findNodeAtPosition: Highlighted node ${highlightedNodeId} exists but cursor is outside its bounds`);
          }
        } else {
          console.log(`[EventFlow] ViewInteractionLayer.findNodeAtPosition: Highlighted node ${highlightedNodeId} exists but DOM element not found`);
        }
      } else {
        console.log(`[EventFlow] ViewInteractionLayer.findNodeAtPosition: Highlighted node ${highlightedNodeId} exists but could not be found in nodeStore`);
      }
    } else {
      console.log(`[EventFlow] ViewInteractionLayer.findNodeAtPosition: No highlighted node available`);
    }

    // If no highlighted node or it's not under the cursor, fall back to direct hit detection

    // Get the current scale from TransformContext
    const scale = transform.scale;

    // Get all nodes
    const nodes = nodeStore.getAllNodes();

    if (nodes.length === 0) {
      console.log(`[EventFlow] ViewInteractionLayer.findNodeAtPosition: No nodes in nodeStore`);
      return null;
    }

    // Log hit detection parameters
    console.log(`[EventFlow] ViewInteractionLayer.findNodeAtPosition: Falling back to direct hit detection. Scale: ${scale.toFixed(2)}, Strict Mode: ${strictMode}`);

    // Try to find nodes using DOM elements first (most accurate)
    const nodeElements = document.querySelectorAll('[data-node-id]');
    console.log(`[EventFlow] ViewInteractionLayer.findNodeAtPosition: Found ${nodeElements.length} node elements in DOM`);

    // First pass: Check if point is directly inside any node's DOM element
    for (let i = 0; i < nodeElements.length; i++) {
      const element = nodeElements[i];
      const nodeId = element.getAttribute('data-node-id');
      if (!nodeId) continue;

      const rect = element.getBoundingClientRect();

      // Log distance from click to node boundaries for debugging
      const distanceTop = screenY - rect.top;
      const distanceBottom = rect.bottom - screenY;
      const distanceLeft = screenX - rect.left;
      const distanceRight = rect.right - screenX;

      const isInside = (
        screenX >= rect.left &&
        screenX <= rect.right &&
        screenY >= rect.top &&
        screenY <= rect.bottom
      );

      console.log(`[EventFlow] ViewInteractionLayer.findNodeAtPosition: DOM check for node ${nodeId}, isInside=${isInside}, distances: top=${distanceTop.toFixed(1)}, bottom=${distanceBottom.toFixed(1)}, left=${distanceLeft.toFixed(1)}, right=${distanceRight.toFixed(1)}`);

      if (isInside) {
        const node = nodeStore.getNode(nodeId);
        if (node) {
          console.log(`[EventFlow] ViewInteractionLayer.findNodeAtPosition: DIRECT DOM HIT on node ${nodeId} (${node.title})`);
          return node;
        }
      }
    }

    // If strictMode is true, we don't want to use model-based detection
    if (strictMode) {
      console.log(`[EventFlow] ViewInteractionLayer.findNodeAtPosition: Strict mode enabled, skipping model-based detection`);
      return null;
    }

    // If DOM-based detection failed, fall back to model-based detection
    // Convert screen coordinates to world coordinates
    const { x, y } = screenToWorld(screenX, screenY);
    console.log(`[EventFlow] ViewInteractionLayer.findNodeAtPosition: Converting screen (${screenX}, ${screenY}) to world (${x.toFixed(2)}, ${y.toFixed(2)})`);

    // Define a maximum distance for model-based hit detection
    // This prevents detecting nodes that are far away from the cursor
    const MAX_DISTANCE_THRESHOLD = 10; // Maximum distance in world units

    let closestNode: NodeModel | null = null;
    let closestDistance = Infinity;

    // Second pass: Check if point is directly inside any node's calculated bounding box
    for (const node of nodes) {
      const nodeLeft = node.position.x;
      const nodeRight = node.position.x + node.dimensions.width;
      const nodeTop = node.position.y;
      const nodeBottom = node.position.y + node.dimensions.height;

      // Log distance from click to node boundaries for debugging
      const distanceTop = y - nodeTop;
      const distanceBottom = nodeBottom - y;
      const distanceLeft = x - nodeLeft;
      const distanceRight = nodeRight - x;

      const isInside = (
        x >= nodeLeft &&
        x <= nodeRight &&
        y >= nodeTop &&
        y <= nodeBottom
      );

      console.log(`[EventFlow] ViewInteractionLayer.findNodeAtPosition: Model check for node ${node.id} (${node.title}), isInside=${isInside}, distances: top=${distanceTop.toFixed(1)}, bottom=${distanceBottom.toFixed(1)}, left=${distanceLeft.toFixed(1)}, right=${distanceRight.toFixed(1)}`);

      if (isInside) {
        console.log(`[EventFlow] ViewInteractionLayer.findNodeAtPosition: MODEL-BASED HIT on node ${node.id} (${node.title})`);
        return node;
      }

      // If not inside, calculate the distance to the node's edge
      // We'll use the closest edge (top, bottom, left, right)
      const distanceToEdge = Math.min(
        Math.abs(distanceTop),
        Math.abs(distanceBottom),
        Math.abs(distanceLeft),
        Math.abs(distanceRight)
      );

      // If this node is closer than the previous closest node, update closestNode
      if (distanceToEdge < closestDistance) {
        closestDistance = distanceToEdge;
        closestNode = node;
      }
    }

    // If we found a node that's close enough, return it
    if (closestNode && closestDistance <= MAX_DISTANCE_THRESHOLD) {
      console.log(`[EventFlow] ViewInteractionLayer.findNodeAtPosition: PROXIMITY HIT on node ${closestNode.id} (${closestNode.title}), distance: ${closestDistance.toFixed(1)}`);
      return closestNode;
    }

    // No node found with direct hit detection or proximity
    console.log(`[EventFlow] ViewInteractionLayer.findNodeAtPosition: NO NODE FOUND with direct hit detection or proximity`);
    return null;
  };

  /**
   * Debug coordinate transformation - disabled for production
   */
  const debugCoordinateTransformation = (scale: number) => {
    // Debug coordinate transformation has been disabled for production
    console.log('[CoordinateDebug] Debug coordinate transformation is disabled');
  };

  /**
   * Show hit detection areas - disabled for production
   */
  const showHitDetectionAreas = () => {
    // Hit detection visualization has been disabled for production
    console.log('[HitDetectionDebug] Hit detection visualization is disabled');

    // Remove any existing debug elements
    const existingDebugElements = document.querySelectorAll('.hit-detection-debug');
    for (let i = 0; i < existingDebugElements.length; i++) {
      const el = existingDebugElements[i];
      el.parentNode?.removeChild(el);
    };
  };

  // Set up global mouse event handlers
  useEffect(() => {
    console.log('[ViewInteractionLayer] Setting up event listeners');

    // Safe wrapper for event handlers to prevent silent failures
    const safeEventHandler = <T extends Event>(handler: (e: T) => void) => {
      return (e: T) => {
        try {
          handler(e);
        } catch (error) {
          console.error('[ViewInteractionLayer] Error in event handler:', error);
        }
      };
    };

    // Flag to control hit detection behavior for fallback detection
    // When true, only direct DOM hits are allowed (no model-based detection)
    // When false, model-based detection is used as a fallback
    const useStrictHitDetection = true; // Set to true to prevent false positives in hit detection

    const handleMouseDown = safeEventHandler((e: MouseEvent) => {
      console.log(`[EventFlow] ViewInteractionLayer.handleMouseDown: clientX=${e.clientX}, clientY=${e.clientY}, button=${e.button}`);

      // Only track left mouse button
      if (e.button !== 0) {
        console.log(`[EventFlow] ViewInteractionLayer.handleMouseDown: IGNORED - not left mouse button`);
        return;
      }

      // Check if a node editor modal is open - if so, ignore mouse events
      const isModalOpen = document.querySelector('.node-editor-backdrop') !== null;
      if (isModalOpen) {
        console.log('[EventFlow] ViewInteractionLayer.handleMouseDown: IGNORED - node editor modal is open');
        return;
      }

      // Reset any existing drag state to ensure we can start a new drag
      if (draggedNodeRef.current) {
        console.log(`[EventFlow] ViewInteractionLayer.handleMouseDown: Clearing existing drag state for node ${draggedNodeRef.current}`);
        nodeController.endDragging();
        draggedNodeRef.current = null;
      }

      // Store the original target element
      originalTargetRef.current = e.target;

      // Check if the click is on a button or other interactive element
      const target = e.target as HTMLElement;
      const isButton = (
        target.tagName === 'BUTTON' ||
        target.closest('button') ||
        target.classList.contains('node-button') ||
        target.closest('.node-button') ||
        target.classList.contains('add-child-button') ||
        target.closest('.add-child-button')
      );

      if (isButton) {
        console.log(`[EventFlow] ViewInteractionLayer.handleMouseDown: IGNORED - click on button (${target.tagName}, class=${target.className})`);
        return; // Don't handle if clicking on a button
      }

      const timestamp = Date.now();
      lastEventTimeRef.current = timestamp;
      mouseDownTimeRef.current = timestamp;
      isMouseDownRef.current = true;

      // Store the mouse position for click vs. drag detection
      mouseDownPosRef.current = { x: e.clientX, y: e.clientY };

      console.log(`[EventFlow] ViewInteractionLayer.handleMouseDown: Calling findNodeAtPosition with strictMode=${useStrictHitDetection}`);

      // First check if we have a highlighted node - this is more reliable than findNodeAtPosition
      let node = null;

      if (highlightedNodeId) {
        node = nodeStore.getNode(highlightedNodeId);
        if (node) {
          console.log(`[EventFlow] ViewInteractionLayer.handleMouseDown: Using highlighted node ${highlightedNodeId}`);
        }
      }

      // If no highlighted node, fall back to findNodeAtPosition
      if (!node) {
        // Find node at click position - use strict mode to only allow direct hits
        node = findNodeAtPosition(e.clientX, e.clientY, useStrictHitDetection);
      }

      // Log the mouse down event
      console.log(`[EventFlow] ViewInteractionLayer.handleMouseDown: Result from node detection: ${node ? `node=${node.id}, title="${node.title}"` : 'no node found'}`);

      // More detailed logging
      console.log('[EventFlow] ViewInteractionLayer.handleMouseDown details:', {
        timestamp,
        clientX: e.clientX,
        clientY: e.clientY,
        button: e.button,
        nodeId: node?.id || 'none',
        nodeTitle: node?.title || 'none',
        target: target.tagName,
        targetClass: target.className,
        targetId: target.id,
        strictHitDetection: useStrictHitDetection,
        highlightedNodeId: highlightedNodeId || 'none',
        usedHighlightedNode: node && node.id === highlightedNodeId
      });

      // If no node was found, don't proceed with node interaction
      if (!node) {
        console.log('[EventFlow] ViewInteractionLayer.handleMouseDown: IGNORED - no node found with hit detection');
        return;
      }

      // Select the node (this replaces the onSelect handler in NodeView)
      console.log(`[EventFlow] ViewInteractionLayer.handleMouseDown: Selecting node ${node.id}`);
      setSelectedNodeId(node.id);

      // CRITICAL: Stop event propagation to prevent canvas panning when interacting with nodes
      // This replaces the e.stopPropagation() that was previously in NodeView.handleMouseDown
      console.log(`[EventFlow] ViewInteractionLayer.handleMouseDown: STOPPING PROPAGATION for node ${node.id}`);
      e.stopPropagation();
      e.preventDefault();

      // Handle root node clicks the same way as other nodes
      // Previously we were letting root node clicks propagate, which required double-clicking
      if (node && !node.relationships.parentId) {
        console.log('[EventFlow] ViewInteractionLayer.handleMouseDown: Root node click detected, handling directly');
        draggedNodeRef.current = node.id;
        // We don't start dragging the root node, but we do track it for click detection
      }

      // If we found a non-root node, start dragging immediately
      if (node && node.relationships.parentId) {
        draggedNodeRef.current = node.id;

        // Get the current layout type
        const isCustomLayout = layoutController.getCurrentLayout() === 'custom';

        // CRITICAL FIX: Get the actual current transform from TransformController
        const currentTransform = transformController.getTransform();

        console.log(`[DragStart] Current transform: scale=${currentTransform.scale.toFixed(4)}, offset=(${currentTransform.offset.x.toFixed(2)}, ${currentTransform.offset.y.toFixed(2)})`);

        // Verify the transform is valid before using it
        if (typeof currentTransform.scale !== 'number' || isNaN(currentTransform.scale)) {
          console.error(`[ViewInteractionLayer] Invalid transform scale in mousedown: ${currentTransform.scale}`);
        }

        if (!currentTransform.offset || typeof currentTransform.offset.x !== 'number' || typeof currentTransform.offset.y !== 'number') {
          console.error(`[ViewInteractionLayer] Invalid transform offset in mousedown: ${JSON.stringify(currentTransform.offset)}`);
        }

        console.log(`[DragStart] Mouse down event at (${e.clientX}, ${e.clientY})`);

        // Transform screen coordinates to canvas coordinates using the correct transform
        const cursorCanvasPosition = CoordinateConverter.screenToCanvas(e.clientX, e.clientY, currentTransform);
        console.log(`[CoordinateDebug] Screen (${e.clientX}, ${e.clientY}) → Canvas (${cursorCanvasPosition.x.toFixed(2)}, ${cursorCanvasPosition.y.toFixed(2)})`);
        console.log(`[CoordinateDebug] Transform: scale=${currentTransform.scale.toFixed(4)}, offset=(${currentTransform.offset.x.toFixed(2)}, ${currentTransform.offset.y.toFixed(2)})`);

        // Log the node's current position for debugging
        console.log(`[CoordinateDebug] Node position before drag: (${node.position.x.toFixed(2)}, ${node.position.y.toFixed(2)})`);
        const nodeScreenPos = CoordinateConverter.canvasToScreen(node.position.x, node.position.y, currentTransform);
        console.log(`[CoordinateDebug] Node screen position before drag: (${nodeScreenPos.x.toFixed(2)}, ${nodeScreenPos.y.toFixed(2)})`);
        console.log(`[CoordinateDebug] Mouse screen position: (${e.clientX.toFixed(2)}, ${e.clientY.toFixed(2)})`);

        // Calculate the offset between mouse and node in both coordinate systems
        const screenOffsetX = e.clientX - nodeScreenPos.x;
        const screenOffsetY = e.clientY - nodeScreenPos.y;
        const canvasOffsetX = cursorCanvasPosition.x - node.position.x;
        const canvasOffsetY = cursorCanvasPosition.y - node.position.y;

        console.log(`[CoordinateDebug] Mouse-Node offset (Screen): (${screenOffsetX.toFixed(2)}, ${screenOffsetY.toFixed(2)})`);
        console.log(`[CoordinateDebug] Mouse-Node offset (Canvas): (${canvasOffsetX.toFixed(2)}, ${canvasOffsetY.toFixed(2)})`);


        // Get initial canvas position using CoordinateConverter with correct transform
        const initialCanvasPos = CoordinateConverter.screenToCanvas(e.clientX, e.clientY, currentTransform);

        // CRITICAL FIX: Store initial positions with clear logging
        console.log(`[DragStart] Mouse screen position: (${e.clientX}, ${e.clientY})`);
        console.log(`[DragStart] Mouse canvas position: (${initialCanvasPos.x.toFixed(2)}, ${initialCanvasPos.y.toFixed(2)})`);
        console.log(`[DragStart] Node position: (${node.position.x.toFixed(2)}, ${node.position.y.toFixed(2)})`);
        console.log(`[DragStart] Current scale: ${currentTransform.scale.toFixed(4)}`);

        dragStartRef.current = {
          // Store initial mouse position in screen coordinates
          mouseX: e.clientX,
          mouseY: e.clientY,
          // Store initial node position
          nodeX: node.position.x,
          nodeY: node.position.y,
          // Store initial canvas position of mouse
          mouseWorldX: initialCanvasPos.x,
          mouseWorldY: initialCanvasPos.y,
          // Store initial scale for reference
          initialScale: currentTransform.scale,
          // No offset needed with direct canvas coordinate calculation
          offsetX: 0,
          offsetY: 0
        };

        // Start dragging with the node's current position
        // NodeController.startDragging expects canvas coordinates

        // Show coordinate debug visualization only if detailed logging is enabled
        if (timestamp % 1000 < 16) { // Only show debug occasionally to reduce overhead
          showCoordinateDebug(e.clientX, e.clientY, node.position);
        }

        // CRITICAL FIX: Start dragging with the node's current position, not the cursor position
        // This prevents the initial jump when dragging starts
        const success = nodeController.startDragging(node.id, node.position, isCustomLayout);

        if (!success) {
          console.error(`[DragProtocol] startDragging failed for node: ${node.id}`);
        }
      }
    });

    const handleMouseMove = safeEventHandler((e: MouseEvent) => {
      // Store last mouse position for debugging purposes
      document.body.setAttribute('data-last-mouse-x', e.clientX.toString());
      document.body.setAttribute('data-last-mouse-y', e.clientY.toString());

      // Only process if mouse is down
      if (!isMouseDownRef.current) return;

      // Check if a node editor modal is open - if so, ignore mouse events
      const isModalOpen = document.querySelector('.node-editor-backdrop') !== null;
      if (isModalOpen) {
        // Don't log this to avoid console spam during modal interactions
        return;
      }

      const timestamp = Date.now();
      lastEventTimeRef.current = timestamp;

      // If we're dragging a node, update its position
      if (draggedNodeRef.current && dragStartRef.current) {
        // Throttle detailed logging to avoid console spam
        const shouldLog = timestamp - lastEventTimeRef.current > 100; // Only log every 100ms

        // Calculate screen delta for logging
        const screenDeltaX = e.clientX - dragStartRef.current.mouseX;
        const screenDeltaY = e.clientY - dragStartRef.current.mouseY;

        // CRITICAL FIX: Get the actual current transform from TransformController
        // The transform from context might be stale
        const currentTransform = transformController.getTransform();

        // Verify the transform is valid before using it
        if (typeof currentTransform.scale !== 'number' || isNaN(currentTransform.scale)) {
          console.error(`[ViewInteractionLayer] Invalid transform scale in mousemove: ${currentTransform.scale}`);
        }

        if (!currentTransform.offset || typeof currentTransform.offset.x !== 'number' || typeof currentTransform.offset.y !== 'number') {
          console.error(`[ViewInteractionLayer] Invalid transform offset in mousemove: ${JSON.stringify(currentTransform.offset)}`);
        }

        // Log the current transform for debugging
        console.log(`[TransformDebug] Current transform during drag: scale=${currentTransform.scale.toFixed(4)}, offset=(${currentTransform.offset.x.toFixed(2)}, ${currentTransform.offset.y.toFixed(2)})`);

        if (shouldLog) {
          console.log(`[DragDebug] Mouse move event at (${e.clientX}, ${e.clientY})`);
          console.log(`[DragDebug] Current transform: scale=${currentTransform.scale.toFixed(4)}, offset=(${currentTransform.offset.x.toFixed(2)}, ${currentTransform.offset.y.toFixed(2)})`);
          console.log(`[DragDebug] Initial node position: (${dragStartRef.current.nodeX.toFixed(2)}, ${dragStartRef.current.nodeY.toFixed(2)})`);
          console.log(`[DragDebug] Initial mouse position: (${dragStartRef.current.mouseX.toFixed(2)}, ${dragStartRef.current.mouseY.toFixed(2)})`);
          console.log(`[DragDebug] Screen delta: (${screenDeltaX.toFixed(2)}, ${screenDeltaY.toFixed(2)})`);
        }

        // Use CoordinateConverter to calculate the new node position
        // CRITICAL FIX: Use the current transform to ensure correct scale
        const nodePosition = CoordinateConverter.calculateNewNodePosition(
          dragStartRef.current.nodeX,
          dragStartRef.current.nodeY,
          dragStartRef.current.mouseX,
          dragStartRef.current.mouseY,
          e.clientX,
          e.clientY,
          currentTransform // Use current transform with correct scale
        );

        // Get canvas delta for logging
        // CRITICAL FIX: Use the current transform to ensure correct scale
        const canvasDelta = CoordinateConverter.calculateCanvasDelta(
          dragStartRef.current.mouseX,
          dragStartRef.current.mouseY,
          e.clientX,
          e.clientY,
          currentTransform // Use current transform with correct scale
        );

        console.log(`[DragCalculation] Canvas delta: (${canvasDelta.dx.toFixed(2)}, ${canvasDelta.dy.toFixed(2)})`);
        console.log(`[DragCalculation] Initial node: (${dragStartRef.current.nodeX.toFixed(2)}, ${dragStartRef.current.nodeY.toFixed(2)})`);
        console.log(`[DragCalculation] Final node: (${nodePosition.x.toFixed(2)}, ${nodePosition.y.toFixed(2)})`);

        // Get current cursor position in canvas coordinates for logging
        // CRITICAL FIX: Use the current transform to ensure correct scale
        const currentCanvasPos = CoordinateConverter.screenToCanvas(e.clientX, e.clientY, currentTransform);
        console.log(`[DragCalculation] Cursor (Canvas): (${currentCanvasPos.x.toFixed(2)}, ${currentCanvasPos.y.toFixed(2)})`);
        console.log(`[DragCalculation] New formula: nodePos = initialNodePos + canvasDelta`);

        // Add detailed logging for debugging
        console.log(`[DragCalculation] Initial node position: (${dragStartRef.current.nodeX}, ${dragStartRef.current.nodeY})`);
        console.log(`[DragCalculation] Initial cursor position (canvas): (${dragStartRef.current.mouseWorldX}, ${dragStartRef.current.mouseWorldY})`);
        console.log(`[DragCalculation] Current cursor position (canvas): (${currentCanvasPos.x.toFixed(2)}, ${currentCanvasPos.y.toFixed(2)})`);
        console.log(`[DragCalculation] Canvas delta: (${canvasDelta.dx.toFixed(2)}, ${canvasDelta.dy.toFixed(2)})`);
        console.log(`[DragCalculation] New node position: (${nodePosition.x.toFixed(2)}, ${nodePosition.y.toFixed(2)})`);
        console.log(`[DragCalculation] Formula: nodePos = initialNodePos + canvasDelta`);
        console.log(`[DragCalculation] Current transform scale: ${currentTransform.scale.toFixed(4)}`);

        // IMPORTANT: NodeController.moveNode expects canvas coordinates
        // In this system, canvas coordinates are the same as world coordinates
        // This is because the transform is applied when rendering, not when storing positions

        // CRITICAL FIX: Direct position update bypassing layout worker
        const node = nodeStore.getNode(draggedNodeRef.current);
        if (node) {
          // Only show coordinate debug if detailed logging is enabled to reduce overhead
          if (shouldLog) {
            showCoordinateDebug(e.clientX, e.clientY, nodePosition);
          }

          // CRITICAL: Direct position update for immediate 1:1 cursor following
          // Bypass the layout worker completely during drag for smooth performance
          nodeStore.updateNode(draggedNodeRef.current, {
            position: nodePosition,
            manuallyPositioned: true
          });

          console.log(`[DragProtocol] Direct position update: node ${draggedNodeRef.current} to (${nodePosition.x.toFixed(2)}, ${nodePosition.y.toFixed(2)})`);
        }

        // Only log to console if shouldLog is true (to avoid console spam)
        if (shouldLog) {
          console.log(`[DragProtocol] 4. MOUSEMOVE during drag, node: ${draggedNodeRef.current}, position: (${e.clientX}, ${e.clientY})`);
          console.log(`[CoordinateDebug] Drag calculation details:`);
          // Get current cursor position in canvas coordinates for logging
          // CRITICAL FIX: Use the current transform to ensure correct scale
          const cursorCanvasPos = CoordinateConverter.screenToCanvas(e.clientX, e.clientY, currentTransform);
          const initialCanvasPos = CoordinateConverter.screenToCanvas(dragStartRef.current.mouseX, dragStartRef.current.mouseY, currentTransform);
          const canvasDelta = CoordinateConverter.calculateCanvasDelta(
            dragStartRef.current.mouseX,
            dragStartRef.current.mouseY,
            e.clientX,
            e.clientY,
            currentTransform // Use current transform with correct scale
          );

          console.log(`[CoordinateDebug] Screen cursor: (${e.clientX}, ${e.clientY})`);
          console.log(`[CoordinateDebug] Initial mouse: (${dragStartRef.current.mouseX}, ${dragStartRef.current.mouseY})`);
          console.log(`[CoordinateDebug] Screen delta: (${screenDeltaX.toFixed(2)}, ${screenDeltaY.toFixed(2)})`);
          console.log(`[CoordinateDebug] Initial canvas pos: (${initialCanvasPos.x.toFixed(2)}, ${initialCanvasPos.y.toFixed(2)})`);
          console.log(`[CoordinateDebug] Current canvas pos: (${cursorCanvasPos.x.toFixed(2)}, ${cursorCanvasPos.y.toFixed(2)})`);
          console.log(`[CoordinateDebug] Canvas delta: (${canvasDelta.dx.toFixed(2)}, ${canvasDelta.dy.toFixed(2)})`);
          console.log(`[CoordinateDebug] Current transform: scale=${currentTransform.scale.toFixed(4)}, offset=(${currentTransform.offset.x.toFixed(2)}, ${currentTransform.offset.y.toFixed(2)})`);
          console.log(`[CoordinateDebug] Initial node: (${dragStartRef.current.nodeX.toFixed(2)}, ${dragStartRef.current.nodeY.toFixed(2)})`);
          console.log(`[CoordinateDebug] New node position: (${nodePosition.x.toFixed(2)}, ${nodePosition.y.toFixed(2)})`);
          console.log(`[CoordinateDebug] Formula: nodePos = initialNodePos + canvasDelta`);
        }

        // Node position is already updated above, no need to call moveNode again
        if (shouldLog) {
          console.log(`[DragProtocol] 5. Node position updated for node: ${draggedNodeRef.current}`);
        }

        // Check for potential relink targets during dragging
        // Only do this check occasionally to avoid performance issues
        if (timestamp % 200 < 16) { // Check roughly every 200ms
          const draggedNode = nodeStore.getNode(draggedNodeRef.current);
          if (draggedNode && draggedNode.relationships.parentId) { // Only allow relinking nodes that have a parent
            const potentialTarget = findPotentialRelinkTarget(draggedNodeRef.current, e.clientX, e.clientY);

            // If we found a potential target that's different from the current parent
            if (potentialTarget && potentialTarget.id !== draggedNode.relationships.parentId) {
              // If it's a new target, update the path visualizer
              if (!potentialRelinkTargetRef.current || potentialRelinkTargetRef.current.id !== potentialTarget.id) {
                console.log(`[RelinkDetection] Found potential relink target during drag: ${potentialTarget.id}`);
                potentialRelinkTargetRef.current = potentialTarget;
                setDraggedNode(draggedNode);
                setTargetNode(potentialTarget);
                setShowRelinkPath(true);
              }
            } else if (potentialRelinkTargetRef.current) {
              // If we had a target but lost it, hide the path visualizer
              console.log(`[RelinkDetection] Lost potential relink target during drag`);
              potentialRelinkTargetRef.current = null;
              setShowRelinkPath(false);
            }
          }
        }
      }
    });

    const handleMouseUp = safeEventHandler((e: MouseEvent) => {
      // Check if a node editor modal is open - if so, ignore mouse events
      const isModalOpen = document.querySelector('.node-editor-backdrop') !== null;
      if (isModalOpen) {
        console.log('[ViewInteractionLayer] Node editor modal is open, ignoring mouse up event');
        return;
      }

      const timestamp = Date.now();
      const dragDuration = timestamp - mouseDownTimeRef.current;

      // First check if we have a highlighted node - this is more reliable than findNodeAtPosition
      let node = null;

      if (highlightedNodeId) {
        node = nodeStore.getNode(highlightedNodeId);
        if (node) {
          console.log(`[EventFlow] ViewInteractionLayer.handleMouseUp: Using highlighted node ${highlightedNodeId}`);
        }
      }

      // If no highlighted node, fall back to findNodeAtPosition
      if (!node) {
        // Find node at release position - use strict mode to only allow direct hits
        node = findNodeAtPosition(e.clientX, e.clientY, useStrictHitDetection);
      }

      // Check if the click is on a button or other interactive element
      const target = e.target as HTMLElement;
      const originalTarget = originalTargetRef.current as HTMLElement;

      // Check both current target and original target (where mousedown occurred)
      const isButton =
        target.tagName === 'BUTTON' ||
        target.closest('button') ||
        target.classList.contains('node-button') ||
        target.closest('.node-button') ||
        target.classList.contains('add-child-button') ||
        target.closest('.add-child-button') ||
        (originalTarget && (
          originalTarget.tagName === 'BUTTON' ||
          originalTarget.closest('button') ||
          originalTarget.classList.contains('node-button') ||
          originalTarget.closest('.node-button') ||
          originalTarget.classList.contains('add-child-button') ||
          originalTarget.closest('.add-child-button')
        ));

      // Log the mouse up event
      console.log('[ViewInteractionLayer] Mouse up at:', {
        timestamp,
        clientX: e.clientX,
        clientY: e.clientY,
        button: e.button,
        wasMouseDown: isMouseDownRef.current,
        nodeId: node?.id || 'none',
        nodeTitle: node?.title || 'none',
        draggedNodeId: draggedNodeRef.current,
        dragDuration,
        target: target.tagName,
        targetClass: target.className,
        targetId: target.id,
        originalTarget: originalTarget ? originalTarget.tagName : 'none',
        originalTargetClass: originalTarget ? originalTarget.className : 'none',
        isButton,
        strictHitDetection: useStrictHitDetection
      });

      // Check if this is a simple click on a node (not a drag)
      // We consider it a simple click if:
      // 1. The mouse was down (isMouseDownRef.current is true)
      // 2. The click was short (dragDuration < CLICK_TIME_THRESHOLD)
      // 3. The mouse didn't move much (distance < CLICK_DISTANCE_THRESHOLD)
      // 4. We found a node at the current position
      // 5. It's not a button click

      // Calculate distance moved
      let distanceMoved = 0;
      if (mouseDownPosRef.current) {
        const dx = e.clientX - mouseDownPosRef.current.x;
        const dy = e.clientY - mouseDownPosRef.current.y;
        distanceMoved = Math.sqrt(dx * dx + dy * dy);
      }

      console.log(`[ClickDetection] Time: ${dragDuration}ms, Distance: ${distanceMoved.toFixed(1)}px, Thresholds: ${CLICK_TIME_THRESHOLD}ms, ${CLICK_DISTANCE_THRESHOLD}px`);

      if (isMouseDownRef.current &&
          dragDuration < CLICK_TIME_THRESHOLD &&
          distanceMoved < CLICK_DISTANCE_THRESHOLD &&
          node &&
          !isButton) {
        console.log(`[NodeInteraction] Simple click detected on node: ${node.id}, opening editor`);
        openNodeEditor(node.id);

        // Clean up any drag state that might have been set
        if (draggedNodeRef.current) {
          console.log(`[NodeInteraction] Ending drag for node ${draggedNodeRef.current} to open editor`);
          nodeController.endDragging();
          draggedNodeRef.current = null;
        }

        isMouseDownRef.current = false;
        originalTargetRef.current = null;
        mouseDownPosRef.current = null;
        return;
      }

      // If we were dragging a node, end the drag
      if (draggedNodeRef.current) {
        console.log(`[DragProtocol] 6. MOUSEUP detected while dragging node: ${draggedNodeRef.current}, position: (${e.clientX}, ${e.clientY}), drag duration: ${dragDuration}ms`);

        // Calculate distance moved for drag detection
        let distanceMoved = 0;
        if (mouseDownPosRef.current) {
          const dx = e.clientX - mouseDownPosRef.current.x;
          const dy = e.clientY - mouseDownPosRef.current.y;
          distanceMoved = Math.sqrt(dx * dx + dy * dy);
        }

        // If drag duration is short and distance is small, treat as a click
        if (dragDuration < CLICK_TIME_THRESHOLD && distanceMoved < CLICK_DISTANCE_THRESHOLD) {
          console.log(`[DragProtocol] 7. Short drag detected (${dragDuration}ms, ${distanceMoved.toFixed(1)}px), treating as click`);

          // Don't open editor if clicking on a button
          if (isButton) {
            console.log(`[DragProtocol] Click detected on button, not opening editor`);
            // End dragging but don't open editor
            const success = nodeController.endDragging();
            console.log(`[DragProtocol] endDragging call ${success ? 'succeeded' : 'failed'} for node: ${draggedNodeRef.current}`);

            // Trigger layout recalculation to ensure proper positioning, but skip view centering
            console.log(`[DragProtocol] Triggering layout recalculation after short drag on button (skipping view centering)`);
            layoutController.applyLayout(false, true);

            draggedNodeRef.current = null;
            isMouseDownRef.current = false;
            return;
          }

          // Get the node to check if it's the root node
          const clickedNode = nodeStore.getNode(draggedNodeRef.current);

          // Find the node in the DOM to simulate a click
          const nodeElement = document.querySelector(`[data-node-id="${draggedNodeRef.current}"]`);
          if (nodeElement || (clickedNode && !clickedNode.relationships.parentId)) {
            // Use the openNodeEditor function from NodeInteractionContext
            console.log(`[DragProtocol] 8. Opening editor for node: ${draggedNodeRef.current} via NodeInteractionContext`);
            openNodeEditor(draggedNodeRef.current);
          }
        } else {
          console.log(`[DragProtocol] 7. Normal drag ended for node: ${draggedNodeRef.current}`);

          // Check for potential relink target
          const draggedNode = nodeStore.getNode(draggedNodeRef.current);
          if (draggedNode && draggedNode.relationships.parentId) { // Only allow relinking nodes that have a parent
            const potentialTarget = findPotentialRelinkTarget(draggedNodeRef.current, e.clientX, e.clientY);

            if (potentialTarget && potentialTarget.id !== draggedNode.relationships.parentId) {
              console.log(`[RelinkDetection] Found valid relink target: ${potentialTarget.id}`);

              // Store the potential target
              potentialRelinkTargetRef.current = potentialTarget;

              // Store the dragged node ID before ending the drag
              const draggedNodeId = draggedNodeRef.current;

              // End the dragging operation first
              console.log(`[RelinkOperation] Ending drag operation before showing relink modal for node: ${draggedNodeId}`);
              nodeController.endDragging();
              draggedNodeRef.current = null;

              // Update state for relink modal and path visualizer
              setDraggedNode(draggedNode);
              setTargetNode(potentialTarget);
              setRelinkModalPosition({ x: e.clientX, y: e.clientY });
              setShowRelinkPath(true);
              setShowRelinkModal(true);

              return;
            }
          }
        }

        // End dragging via NodeController
        const success = nodeController.endDragging();
        console.log(`[DragProtocol] 9. endDragging call ${success ? 'succeeded' : 'failed'} for node: ${draggedNodeRef.current}`);

        // Trigger layout recalculation to ensure proper positioning, but skip view centering
        console.log(`[DragProtocol] Triggering layout recalculation after drag end (skipping view centering)`);
        layoutController.applyLayout(false, true);

        const draggedNodeId = draggedNodeRef.current;
        draggedNodeRef.current = null;
        console.log(`[DragProtocol] 10. Cleared draggedNodeRef for node: ${draggedNodeId}`);
      }

      // Reset all state to ensure clean slate for next interaction
      isMouseDownRef.current = false;
      originalTargetRef.current = null;
      mouseDownPosRef.current = null;

      // Make sure drag state is fully cleared
      if (draggedNodeRef.current) {
        console.log(`[EventFlow] ViewInteractionLayer.handleMouseUp: Ensuring drag state is cleared for node ${draggedNodeRef.current}`);
        nodeController.endDragging();
        draggedNodeRef.current = null;
      }
    });

    // Handle wheel events for zooming - SINGLE CAPTURE POINT FOR ALL WHEEL EVENTS
    const handleWheel = safeEventHandler((e: WheelEvent) => {
      console.log('[ZoomSystem] ViewInteractionLayer captured wheel event');

      // Skip if a node is being dragged
      if (document.body.classList.contains('node-dragging')) {
        console.log('[ZoomSystem] Skipping wheel event - node is being dragged');
        return;
      }

      // Check if a node editor modal is open
      const isModalOpen = document.querySelector('.node-editor-backdrop') !== null;
      if (isModalOpen) {
        console.log('[ZoomSystem] Node editor modal is open');

        // Special case: If we're over a node editor, allow scrolling
        const editorElement = document.querySelector('.node-editor-modal');
        if (editorElement) {
          const rect = editorElement.getBoundingClientRect();
          const isOverEditor = (
            e.clientX >= rect.left &&
            e.clientX <= rect.right &&
            e.clientY >= rect.top &&
            e.clientY <= rect.bottom
          );

          if (isOverEditor) {
            console.log('[ZoomSystem] Over node editor, allowing scroll');
            // Let the event propagate for scrolling
            return;
          } else {
            console.log('[ZoomSystem] Outside node editor, checking for zoom out to close');

            // If we're outside the editor and scrolling down (zooming out), close the editor
            if (e.deltaY > 0) {
              console.log('[ZoomSystem] Zooming out outside editor, closing node editor');

              // Close the node editor
              if (nodeController && typeof nodeController.closeNodeEditor === 'function') {
                nodeController.closeNodeEditor();
              } else {
                // Fallback if nodeController is not available
                window.dispatchEvent(new CustomEvent('mvc-close-node-editor'));
              }

              // Prevent default to avoid page scrolling
              try {
                e.preventDefault();
              } catch (error) {
                console.warn('[ZoomSystem] Unable to preventDefault inside passive event listener.');
              }

              // After closing, allow the zoom to continue
              const rect = document.body.getBoundingClientRect();
              const mouseX = e.clientX - rect.left;
              const mouseY = e.clientY - rect.top;

              // Use the ZoomController to handle the zoom
              zoomController.handleZoom(e, mouseX, mouseY);

              // Stop propagation
              e.stopPropagation();
              return;
            } else {
              // If zooming in while outside the editor, just prevent default
              try {
                e.preventDefault();
              } catch (error) {
                console.warn('[ZoomSystem] Unable to preventDefault inside passive event listener.');
              }
              return;
            }
          }
        }
      }

      // Prevent default to avoid page scrolling
      try {
        e.preventDefault();
      } catch (error) {
        console.warn('[ZoomSystem] Unable to preventDefault inside passive event listener.');
      }

      // Get mouse position relative to container
      const rect = document.body.getBoundingClientRect();
      const mouseX = e.clientX - rect.left;
      const mouseY = e.clientY - rect.top;

      // Use the ZoomController to handle the zoom
      zoomController.handleZoom(e, mouseX, mouseY);

      // Stop propagation to prevent other components from handling this event
      e.stopPropagation();
    });

    // Add event listeners with capture phase (true as third parameter)
    // This ensures we capture events before they reach other handlers
    window.addEventListener('mousedown', handleMouseDown, true);
    window.addEventListener('mousemove', handleMouseMove, true);
    window.addEventListener('mouseup', handleMouseUp, true);
    window.addEventListener('wheel', handleWheel, { passive: false, capture: true });

    // Also listen for potential issues that might interfere with our tracking
    window.addEventListener('blur', () => {
      console.log('[ViewInteractionLayer] Window blur event - may affect tracking');
    }, true);

    window.addEventListener('focus', () => {
      console.log('[ViewInteractionLayer] Window focus event');
    }, true);

    // Clean up event listeners
    return () => {
      console.log('[ViewInteractionLayer] Cleaning up event listeners');
      window.removeEventListener('mousedown', handleMouseDown, true);
      window.removeEventListener('mousemove', handleMouseMove, true);
      window.removeEventListener('mouseup', handleMouseUp, true);
      window.removeEventListener('wheel', handleWheel, { passive: false, capture: true } as EventListenerOptions);
      window.removeEventListener('blur', () => {}, true);
      window.removeEventListener('focus', () => {}, true);

      // Ensure we reset the state on unmount
      isMouseDownRef.current = false;
    };
  }, []); // Empty dependency array to prevent unnecessary remounts

  // Hit detection visualization state has been disabled for production
  // const [showingHitAreas, setShowingHitAreas] = useState(false);

  // Debug keyboard shortcuts have been disabled
  // useEffect(() => {
  //   // Debug keyboard shortcuts - disabled for production
  // }, []);

  // Set up effect to clear highlighted node on document click
  useEffect(() => {
    const handleDocumentClick = (e: MouseEvent) => {
      // Log the click target for debugging
      const target = e.target as HTMLElement;
      console.log(`[HoverHitDetection] Document click detected on:`, {
        tagName: target.tagName,
        className: target.className,
        id: target.id,
        isNodeElement: target.closest('[data-node-id]') !== null,
        highlightedNodeId
      });

      // Clear highlighted node on any click in the document
      if (highlightedNodeId) {
        console.log(`[HoverHitDetection] Clearing highlighted node on document click: ${highlightedNodeId}`);
        setHighlightedNodeId(null);
      }
    };

    // Also clear highlighted node when mouse leaves the canvas
    const handleMouseLeave = () => {
      if (highlightedNodeId) {
        console.log(`[HoverHitDetection] Clearing highlighted node on canvas mouse leave: ${highlightedNodeId}`);
        setHighlightedNodeId(null);
      }
    };

    // Find the canvas element
    const canvasElement = document.querySelector('.canvas-container');
    if (canvasElement) {
      canvasElement.addEventListener('mouseleave', handleMouseLeave);
    }

    document.addEventListener('click', handleDocumentClick);
    return () => {
      document.removeEventListener('click', handleDocumentClick);
      if (canvasElement) {
        canvasElement.removeEventListener('mouseleave', handleMouseLeave);
      }
    };
  }, [highlightedNodeId]);

  /**
   * Find potential relink target for a dragged node
   * @param draggedNodeId The ID of the node being dragged
   * @param screenX Screen X coordinate
   * @param screenY Screen Y coordinate
   * @returns The potential target node, or null if none found
   */
  const findPotentialRelinkTarget = (draggedNodeId: string, screenX: number, screenY: number): NodeModel | null => {
    // Get the dragged node
    const draggedNode = nodeStore.getNode(draggedNodeId);
    if (!draggedNode) return null;

    // Convert screen coordinates to world coordinates
    const { x, y } = screenToWorld(screenX, screenY);

    // Get all nodes
    const nodes = nodeStore.getAllNodes();

    // Define parameters for relink detection
    // Use scale from TransformContext
    const scale = transform.scale;

    // Minimum overlap percentage threshold to trigger relink (increased from 20% to 30% for more precision)
    const MIN_OVERLAP_PERCENTAGE = 30;

    console.log(`[RelinkDetection] Scale: ${scale.toFixed(2)}, Checking for overlaps with minimum ${MIN_OVERLAP_PERCENTAGE}% threshold`);
    console.log(`[RelinkDetection] Checking for potential relink targets at world position (${x.toFixed(2)}, ${y.toFixed(2)})`);
    console.log(`[RelinkDetection] Total nodes to check: ${nodes.length}`);
    console.log(`[RelinkDetection] Dragged node: ${draggedNode.id}, position: (${draggedNode.position.x.toFixed(2)}, ${draggedNode.position.y.toFixed(2)}), dimensions: ${draggedNode.dimensions.width}x${draggedNode.dimensions.height}`);

    // Calculate dragged node bounding box
    const draggedNodeLeft = draggedNode.position.x;
    const draggedNodeRight = draggedNode.position.x + draggedNode.dimensions.width;
    const draggedNodeTop = draggedNode.position.y;
    const draggedNodeBottom = draggedNode.position.y + draggedNode.dimensions.height;
    const draggedNodeArea = draggedNode.dimensions.width * draggedNode.dimensions.height;

    // Find the node with the highest overlap percentage
    let bestTargetNode: NodeModel | null = null;
    let highestOverlapPercentage = 0;

    for (const node of nodes) {
      // Skip the dragged node itself
      if (node.id === draggedNodeId) {
        console.log(`[RelinkDetection] Skipping dragged node ${node.id}`);
        continue;
      }

      // Skip nodes that would create a cycle
      if (nodeController.isDescendant(node.id, draggedNodeId)) {
        console.log(`[RelinkDetection] Skipping node ${node.id} as it would create a cycle`);
        continue;
      }

      // Calculate target node bounding box
      const targetNodeLeft = node.position.x;
      const targetNodeRight = node.position.x + node.dimensions.width;
      const targetNodeTop = node.position.y;
      const targetNodeBottom = node.position.y + node.dimensions.height;

      // Check for bounding box overlap
      const hasOverlap = !(
        draggedNodeRight < targetNodeLeft ||
        draggedNodeLeft > targetNodeRight ||
        draggedNodeBottom < targetNodeTop ||
        draggedNodeTop > targetNodeBottom
      );

      if (hasOverlap) {
        // Calculate overlap area
        const overlapWidth = Math.min(draggedNodeRight, targetNodeRight) - Math.max(draggedNodeLeft, targetNodeLeft);
        const overlapHeight = Math.min(draggedNodeBottom, targetNodeBottom) - Math.max(draggedNodeTop, targetNodeTop);
        const overlapArea = overlapWidth * overlapHeight;

        // Calculate overlap percentage relative to the dragged node's area
        const overlapPercentage = (overlapArea / draggedNodeArea) * 100;

        console.log(`[RelinkDetection] Node ${node.id} overlaps with ${overlapPercentage.toFixed(2)}% of dragged node area`);

        // Check if this overlap is better than our current best
        if (overlapPercentage > highestOverlapPercentage && overlapPercentage >= MIN_OVERLAP_PERCENTAGE) {
          highestOverlapPercentage = overlapPercentage;
          bestTargetNode = node;
          console.log(`[RelinkDetection] New best target: ${node.id} with ${overlapPercentage.toFixed(2)}% overlap`);
        }
      } else {
        // If no overlap, calculate distance for debugging purposes
        const nodeCenterX = node.position.x + node.dimensions.width / 2;
        const nodeCenterY = node.position.y + node.dimensions.height / 2;
        const draggedNodeCenterX = draggedNode.position.x + draggedNode.dimensions.width / 2;
        const draggedNodeCenterY = draggedNode.position.y + draggedNode.dimensions.height / 2;
        const distance = Math.sqrt(
          Math.pow(draggedNodeCenterX - nodeCenterX, 2) +
          Math.pow(draggedNodeCenterY - nodeCenterY, 2)
        );
        console.log(`[RelinkDetection] Node ${node.id} has no overlap, distance between centers: ${distance.toFixed(2)}`);
      }
    }

    if (bestTargetNode) {
      console.log(`[RelinkDetection] Found potential relink target: ${bestTargetNode.id} with ${highestOverlapPercentage.toFixed(2)}% overlap`);
    } else {
      console.log(`[RelinkDetection] No suitable relink target found with minimum ${MIN_OVERLAP_PERCENTAGE}% overlap`);
    }

    return bestTargetNode;
  };

  /**
   * Handle relinking a node to a new parent
   * @param sourceNodeId The ID of the node to relink
   * @param targetNodeId The ID of the new parent node
   * @param withSubtree Whether to relink the entire subtree
   */
  const handleRelink = (sourceNodeId: string, targetNodeId: string, withSubtree: boolean) => {
    console.log(`[RelinkOperation] Relinking node ${sourceNodeId} to ${targetNodeId}, withSubtree: ${withSubtree}`);

    // Close the relink modal
    setShowRelinkModal(false);
    setShowRelinkPath(false);

    // Perform the relink operation
    const success = nodeController.relinkNodeWithOptions(sourceNodeId, targetNodeId, { withSubtree });

    if (success) {
      console.log(`[RelinkOperation] Successfully relinked node ${sourceNodeId} to ${targetNodeId}`);
    } else {
      console.error(`[RelinkOperation] Failed to relink node ${sourceNodeId} to ${targetNodeId}`);
    }

    // Reset state
    potentialRelinkTargetRef.current = null;
  };

  /**
   * Cancel the relink operation
   */
  const handleCancelRelink = () => {
    console.log('[RelinkOperation] Relink operation cancelled');
    setShowRelinkModal(false);
    setShowRelinkPath(false);
    potentialRelinkTargetRef.current = null;
  };

  // Render the relink modal and path visualizer
  return (
    <HighlightedNodeContext.Provider value={{ highlightedNodeId, registerHighlightedNode }}>
      {showRelinkPath && draggedNode && targetNode && (
        <RelinkPathVisualizer
          sourcePosition={draggedNode.position}
          targetPosition={targetNode.position}
          isActive={showRelinkPath}
        />
      )}

      {showRelinkModal && draggedNode && targetNode && (
        <RelinkModal
          position={relinkModalPosition}
          hasChildren={draggedNode.relationships.childIds.length > 0}
          onLinkNodeOnly={() => handleRelink(draggedNode.id, targetNode.id, false)}
          onLinkSubtree={() => handleRelink(draggedNode.id, targetNode.id, true)}
          onCancel={handleCancelRelink}
        />
      )}
    </HighlightedNodeContext.Provider>
  );
};

// Export the component as default
export default ViewInteractionLayer;
