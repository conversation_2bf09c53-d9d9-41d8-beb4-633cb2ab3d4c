/**
 * TagManager - Component for managing tags
 *
 * This component provides UI for creating, editing, and deleting tags,
 * as well as filtering nodes by tags.
 */

import React, { useState, useEffect } from 'react';
import { useTags } from '../context/TagContext';
import { Tag } from '../types';
import { filterController, FilterType } from '../controller/FilterController';
import './TagManager.css';

interface TagManagerProps {
  onClose?: () => void;
}

export const TagManager: React.FC<TagManagerProps> = ({ onClose }) => {
  const { tags, addTag, removeTag, updateTag, canCreateTags } = useTags();
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [newTagName, setNewTagName] = useState('');
  const [newTagColor, setNewTagColor] = useState('#4299e1');
  const [editingTag, setEditingTag] = useState<Tag | null>(null);

  // Load current filter on mount
  useEffect(() => {
    // Get current filter
    const tagFilter = filterController.getFilter(FilterType.TAG);
    if (tagFilter && tagFilter.active && Array.isArray(tagFilter.value)) {
      setSelectedTags(tagFilter.value as string[]);
    }
  }, []);

  // Handle tag selection
  const handleTagSelect = (tagId: string) => {
    setSelectedTags(prev => {
      const newSelectedTags = prev.includes(tagId)
        ? prev.filter(id => id !== tagId)
        : [...prev, tagId];

      // Apply filter
      filterController.setTagFilter(newSelectedTags, newSelectedTags.length > 0);

      return newSelectedTags;
    });
  };

  // Handle tag creation
  const handleCreateTag = () => {
    if (newTagName.trim() === '') return;

    addTag(newTagName.trim(), newTagColor);

    // Reset form
    setNewTagName('');
    setNewTagColor('#4299e1');
  };

  // Handle tag update
  const handleUpdateTag = () => {
    if (!editingTag || newTagName.trim() === '') return;

    updateTag(editingTag.id, {
      name: newTagName.trim(),
      color: newTagColor
    });

    // Reset form
    setEditingTag(null);
    setNewTagName('');
    setNewTagColor('#4299e1');
  };

  // Handle tag deletion
  const handleDeleteTag = (tagId: string) => {
    // Remove from selected tags
    setSelectedTags(prev => {
      const newSelectedTags = prev.filter(id => id !== tagId);

      // Apply filter
      filterController.setTagFilter(newSelectedTags, newSelectedTags.length > 0);

      return newSelectedTags;
    });

    // Delete the tag
    removeTag(tagId);
  };

  // Handle edit button click
  const handleEditClick = (tag: Tag) => {
    setEditingTag(tag);
    setNewTagName(tag.name);
    setNewTagColor(tag.color);
  };

  // Handle cancel edit
  const handleCancelEdit = () => {
    setEditingTag(null);
    setNewTagName('');
    setNewTagColor('#4299e1');
  };

  // Clear all filters
  const handleClearFilters = () => {
    setSelectedTags([]);
    filterController.clearFilters();
  };

  // Predefined colors
  const predefinedColors = [
    '#4299e1', // Blue
    '#48bb78', // Green
    '#ed8936', // Orange
    '#9f7aea', // Purple
    '#f56565', // Red
    '#ecc94b', // Yellow
    '#38b2ac', // Teal
    '#f687b3', // Pink
    '#a0aec0'  // Gray
  ];

  return (
    <div className="tag-manager">
      <div className="tag-manager-header">
        <h2>Tag Manager</h2>
        {onClose && (
          <button className="close-button" onClick={onClose}>×</button>
        )}
      </div>

      <div className="tag-manager-content">
        <div className="tag-list">
          <h3>Available Tags</h3>
          {tags.length === 0 ? (
            <p className="no-tags">No tags available. Create your first tag below.</p>
          ) : (
            <ul>
              {tags.map(tag => (
                <li key={tag.id} className={`tag-item ${selectedTags.includes(tag.id) ? 'selected' : ''}`}>
                  <div
                    className="tag-color"
                    style={{ backgroundColor: tag.color }}
                    onClick={() => handleTagSelect(tag.id)}
                  ></div>
                  <span
                    className="tag-name"
                    onClick={() => handleTagSelect(tag.id)}
                  >
                    {tag.name}
                  </span>
                  <div className="tag-actions">
                    <button
                      className="edit-button"
                      onClick={() => handleEditClick(tag)}
                    >
                      Edit
                    </button>
                    <button
                      className="delete-button"
                      onClick={() => handleDeleteTag(tag.id)}
                    >
                      Delete
                    </button>
                  </div>
                </li>
              ))}
            </ul>
          )}

          {selectedTags.length > 0 && (
            <button
              className="clear-filters-button"
              onClick={handleClearFilters}
            >
              Clear Filters
            </button>
          )}
        </div>

        <div className="tag-form">
          <h3>{editingTag ? 'Edit Tag' : 'Create New Tag'}</h3>
          <div className="form-group">
            <label htmlFor="tagName">Tag Name</label>
            <input
              type="text"
              id="tagName"
              value={newTagName}
              onChange={e => setNewTagName(e.target.value)}
              placeholder="Enter tag name"
            />
          </div>

          <div className="form-group">
            <label>Tag Color</label>
            <div className="color-picker">
              {predefinedColors.map(color => (
                <div
                  key={color}
                  className={`color-option ${newTagColor === color ? 'selected' : ''}`}
                  style={{ backgroundColor: color }}
                  onClick={() => setNewTagColor(color)}
                ></div>
              ))}
              <input
                type="color"
                value={newTagColor}
                onChange={e => setNewTagColor(e.target.value)}
                className="custom-color-picker"
              />
            </div>
          </div>

          <div className="form-actions">
            {editingTag ? (
              <>
                <button
                  className="update-button"
                  onClick={handleUpdateTag}
                  disabled={newTagName.trim() === ''}
                >
                  Update Tag
                </button>
                <button
                  className="cancel-button"
                  onClick={handleCancelEdit}
                >
                  Cancel
                </button>
              </>
            ) : (
              <button
                className="create-button"
                onClick={handleCreateTag}
                disabled={newTagName.trim() === ''}
              >
                Create Tag
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
